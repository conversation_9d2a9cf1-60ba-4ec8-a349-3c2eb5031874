import mongoose from 'mongoose';
import User from './models/User.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const enableSubtaskNotifications = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Enable subtask email notifications for all users
    const result = await User.updateMany(
      {},
      {
        $set: {
          'notificationPreferences.subtaskSubmission.email': true,
          'notificationPreferences.subtaskConfirmation.email': true,
          'notificationPreferences.subtaskRejection.email': true
        }
      }
    );
    
    console.log('✅ Updated', result.modifiedCount, 'users to enable subtask email notifications');

    // Verify the changes
    const users = await User.find({}).select('name email notificationPreferences.subtaskSubmission notificationPreferences.subtaskConfirmation notificationPreferences.subtaskRejection');
    console.log('\n📋 Updated notification preferences:');
    users.forEach(user => {
      console.log('👤', user.name + ':',
        'Submission =', user.notificationPreferences?.subtaskSubmission?.email ? 'ON' : 'OFF',
        'Confirmation =', user.notificationPreferences?.subtaskConfirmation?.email ? 'ON' : 'OFF',
        'Rejection =', user.notificationPreferences?.subtaskRejection?.email ? 'ON' : 'OFF'
      );
    });

    console.log('\n✅ Done! All users will now receive email notifications for collaborative subtask activities.');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
};

enableSubtaskNotifications();
