import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  Clock,
  User,
  UserPlus,
  Trophy,
  Star,
  AlertTriangle,
  Info,
  X,
  Eye,
  ExternalLink
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

const NotificationCard = ({ notification, onMarkAsRead, onDelete }) => {
  const navigate = useNavigate();
  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    const iconProps = { size: 18, className: "flex-shrink-0" };
    
    switch (type) {
      case 'TASK_ASSIGNED':
        return <User {...iconProps} className="text-blue-500" />;
      case 'TASK_COMPLETED':
        return <CheckCircle {...iconProps} className="text-green-500" />;
      case 'TASK_UPDATED':
        return <Clock {...iconProps} className="text-orange-500" />;
      case 'TASK_OVERDUE':
        return <AlertTriangle {...iconProps} className="text-red-500" />;
      case 'TASK_DUE_SOON':
        return <Clock {...iconProps} className="text-yellow-500" />;
      case 'FRIEND_REQUEST_SENT':
      case 'FRIEND_REQUEST_ACCEPTED':
        return <UserPlus {...iconProps} className="text-purple-500" />;
      case 'BADGE_EARNED':
        return <Trophy {...iconProps} className="text-yellow-500" />;
      case 'LEVEL_UP':
        return <Star {...iconProps} className="text-gold-500" />;
      case 'MISSION_COMPLETED':
        return <Trophy {...iconProps} className="text-green-500" />;
      case 'XP_GAINED':
        return <Star {...iconProps} className="text-blue-500" />;
      case 'SYSTEM_ANNOUNCEMENT':
        return <Info {...iconProps} className="text-blue-500" />;
      default:
        return <Info {...iconProps} className="text-gray-500" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'HIGH':
      case 'URGENT':
        return 'border-l-red-500';
      case 'MEDIUM':
        return 'border-l-yellow-500';
      case 'LOW':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-300';
    }
  };

  // Handle notification click (mark as read if unread)
  const handleNotificationClick = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification._id);
    }
  };

  // Handle action button clicks
  const handleActionClick = (action, e) => {
    e.stopPropagation();

    switch (action.action) {
      case 'VIEW_TASK':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'VIEW_GALLERY':
        navigate('/user/gallery');
        break;
      case 'REVIEW_CONFIRM':
      case 'REVIEW_AND_CONFIRM':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'REVISE_RESUBMIT':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'VIEW_EVIDENCE':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'ACCEPT_FRIEND_REQUEST':
        // Handle friend request acceptance
        console.log('Accept friend request:', notification.relatedUser);
        // TODO: Implement friend request acceptance API call
        break;
      case 'DECLINE_FRIEND_REQUEST':
        // Handle friend request decline
        console.log('Decline friend request:', notification.relatedUser);
        // TODO: Implement friend request decline API call
        break;
      case 'VIEW_PROFILE':
        if (notification.relatedUser) {
          navigate(`/users/${notification.relatedUser._id}`);
        }
        break;
      case 'VIEW_DASHBOARD':
        navigate('/user/dashboard');
        break;
      case 'VIEW_TASKS':
        navigate('/user/tasks');
        break;
      case 'CREATE_TASK':
        navigate('/user/create-task');
        break;
      default:
        console.log('Unknown action:', action.action);
    }

    // Mark notification as read when action is clicked
    if (!notification.isRead) {
      onMarkAsRead(notification._id);
    }
  };

  const timeAgo = formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true });

  return (
    <div
      onClick={handleNotificationClick}
      className={`relative p-4 hover:bg-gray-50 transition-colors cursor-pointer border-l-4 ${getPriorityColor(notification.priority)} ${
        !notification.isRead ? 'bg-blue-50/30' : ''
      }`}
    >
      {/* Unread indicator */}
      {!notification.isRead && (
        <div className="absolute top-4 right-4">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        </div>
      )}

      {/* Delete button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete(notification._id);
        }}
        className="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors opacity-0 group-hover:opacity-100"
      >
        <X size={14} />
      </button>

      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0 mt-0.5">
          {getNotificationIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center justify-between mb-1">
            <h4 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
              {notification.title}
            </h4>
            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
              {timeAgo}
            </span>
          </div>

          {/* Message */}
          <p className={`text-sm ${!notification.isRead ? 'text-gray-800' : 'text-gray-600'} mb-2`}>
            {notification.message}
          </p>

          {/* Sender info */}
          {notification.sender && (
            <div className="flex items-center space-x-2 mb-2">
              <img
                src={notification.sender.profileImageUrl || "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iMTIiIGZpbGw9IiMzQjgyRjYiLz4KPHRleHQgeD0iMTIiIHk9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmb250LXdlaWdodD0iYm9sZCI+VTwvdGV4dD4KPHN2Zz4K"}
                alt={notification.sender.name}
                className="w-5 h-5 rounded-full object-cover"
                onError={(e) => {
                  e.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iMTIiIGZpbGw9IiMzQjgyRjYiLz4KPHRleHQgeD0iMTIiIHk9IjE2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmb250LXdlaWdodD0iYm9sZCI+VTwvdGV4dD4KPHN2Zz4K";
                }}
              />
              <span className="text-xs text-gray-500">
                from {notification.sender.name}
              </span>
            </div>
          )}

          {/* Related task info */}
          {notification.relatedTask && (
            <div className="flex items-center space-x-2 mb-2">
              <div className={`w-2 h-2 rounded-full ${
                notification.relatedTask.status === 'Completed' ? 'bg-green-500' :
                notification.relatedTask.status === 'In Progress' ? 'bg-yellow-500' :
                'bg-gray-400'
              }`}></div>
              <span className="text-xs text-gray-500">
                Task: {notification.relatedTask.title}
              </span>
              {notification.relatedTask.priority && (
                <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                  notification.relatedTask.priority === 'High' ? 'bg-red-100 text-red-700' :
                  notification.relatedTask.priority === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {notification.relatedTask.priority}
                </span>
              )}
            </div>
          )}

          {/* Action buttons */}
          {notification.actions && notification.actions.length > 0 && (
            <div className="flex items-center space-x-2 mt-3">
              {notification.actions.map((action, index) => (
                <button
                  key={index}
                  onClick={(e) => handleActionClick(action, e)}
                  className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                    action.style === 'PRIMARY'
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : action.style === 'SECONDARY'
                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {action.label}
                  {action.action === 'VIEW_TASK' && <ExternalLink size={12} className="ml-1 inline" />}
                </button>
              ))}
            </div>
          )}

          {/* Mark as read button for unread notifications */}
          {!notification.isRead && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onMarkAsRead(notification._id);
              }}
              className="flex items-center space-x-1 mt-2 text-xs text-blue-600 hover:text-blue-700 transition-colors"
            >
              <Eye size={12} />
              <span>Mark as read</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationCard;
