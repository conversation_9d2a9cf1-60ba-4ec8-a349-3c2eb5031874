import { sendEmail } from '../config/emailConfig.js';
import {
  createTaskAssignmentEmail,
  createTaskCompletionEmail,
  createFriendRequestEmail,
  createBadgeEarnedEmail,
  createSystemAnnouncementEmail,
  createSubtaskSubmissionEmail,
  createSubtaskConfirmationEmail,
  createSubtaskRejectionEmail
} from '../utils/emailTemplates.js';
import User from '../models/User.js';
import Task from '../models/Task.js';

class EmailService {
  // Send task assignment email notification
  static async sendTaskAssignmentNotification(taskData, assignedUserIds, creatorId) {
    try {
      const creator = await User.findById(creatorId).select('name email');
      if (!creator) return;

      for (const userId of assignedUserIds) {
        if (userId.toString() === creatorId.toString()) continue;
        
        const assignedUser = await User.findById(userId).select('name email notificationPreferences');
        if (!assignedUser || !assignedUser.email) continue;

        // Check if user wants email notifications for task assignments
        const emailPrefs = assignedUser.notificationPreferences?.taskAssigned;
        if (!emailPrefs?.email) continue;

        const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/task/${taskData._id}`;
        
        const emailData = createTaskAssignmentEmail({
          recipientName: assignedUser.name,
          senderName: creator.name,
          task: {
            title: taskData.title,
            description: taskData.description,
            priority: taskData.priority,
            status: taskData.status,
            dueDate: taskData.dueDate
          },
          actionUrl
        });

        await sendEmail({
          to: assignedUser.email,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text
        });

        console.log(`✅ Task assignment email sent to ${assignedUser.email}`);
      }
    } catch (error) {
      console.error('❌ Error sending task assignment emails:', error.message);
    }
  }

  // Send task completion email notification
  static async sendTaskCompletionNotification(taskData, completedByUserId) {
    try {
      const completedByUser = await User.findById(completedByUserId).select('name email');
      if (!completedByUser) return;

      const task = await Task.findById(taskData._id).populate('assignedTo', 'name email notificationPreferences');
      if (!task) return;

      // Notify all assigned users (except the one who completed it)
      for (const assignedUser of task.assignedTo) {
        if (assignedUser._id.toString() === completedByUserId.toString()) continue;
        if (!assignedUser.email) continue;

        // Check if user wants email notifications for task completions
        const emailPrefs = assignedUser.notificationPreferences?.taskCompleted;
        if (!emailPrefs?.email) continue;

        const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/task/${taskData._id}`;
        
        const emailData = createTaskCompletionEmail({
          recipientName: assignedUser.name,
          completedByName: completedByUser.name,
          task: {
            title: taskData.title,
            description: taskData.description,
            priority: taskData.priority,
            status: taskData.status
          },
          actionUrl
        });

        await sendEmail({
          to: assignedUser.email,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text
        });

        console.log(`✅ Task completion email sent to ${assignedUser.email}`);
      }
    } catch (error) {
      console.error('❌ Error sending task completion emails:', error.message);
    }
  }

  // Send friend request email notification
  static async sendFriendRequestNotification(senderUserId, recipientUserId) {
    try {
      const [sender, recipient] = await Promise.all([
        User.findById(senderUserId).select('name email'),
        User.findById(recipientUserId).select('name email notificationPreferences')
      ]);

      if (!sender || !recipient || !recipient.email) return;

      // Check if user wants email notifications for friend requests
      const emailPrefs = recipient.notificationPreferences?.friendRequest;
      if (!emailPrefs?.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/manage-users`;

      const emailData = createFriendRequestEmail({
        recipientName: recipient.name,
        senderName: sender.name,
        senderEmail: sender.email,
        actionUrl
      });

      await sendEmail({
        to: recipient.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Friend request email sent to ${recipient.email}`);
    } catch (error) {
      console.error('❌ Error sending friend request email:', error.message);
    }
  }

  // Send friend request accepted notification email
  static async sendFriendRequestAcceptedNotification(accepterId, senderId) {
    try {
      const accepter = await User.findById(accepterId).select('name email');
      const sender = await User.findById(senderId).select('name email notificationPreferences');

      if (!accepter || !sender || !sender.email) return;

      // Check if user wants email notifications for friend request accepted
      const emailPrefs = sender.notificationPreferences?.friendRequestAccepted;
      if (!emailPrefs?.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/profile/${accepterId}`;

      const emailData = createSystemAnnouncementEmail({
        recipientName: sender.name,
        title: 'Friend Request Accepted! 🎉',
        message: `Great news! ${accepter.name} has accepted your friend request.\n\nYou are now connected and can:\n• Assign tasks to each other\n• View each other's profiles\n• Collaborate on projects\n\nStart collaborating by visiting their profile or creating a task together!`,
        actionUrl
      });

      await sendEmail({
        to: sender.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Friend request accepted email sent to ${sender.email}`);
    } catch (error) {
      console.error('❌ Error sending friend request accepted email:', error.message);
    }
  }

  // Send badge earned email notification
  static async sendBadgeEarnedNotification(userId, badgeData) {
    try {
      const user = await User.findById(userId).select('name email notificationPreferences');
      if (!user || !user.email) return;

      // Check if user wants email notifications for badges
      const emailPrefs = user.notificationPreferences?.badgeEarned;
      if (!emailPrefs?.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/gallery`;
      
      const emailData = createBadgeEarnedEmail({
        recipientName: user.name,
        badgeName: badgeData.name,
        badgeDescription: badgeData.description,
        actionUrl
      });

      await sendEmail({
        to: user.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Badge earned email sent to ${user.email}`);
    } catch (error) {
      console.error('❌ Error sending badge earned email:', error.message);
    }
  }

  // Send system announcement email
  static async sendSystemAnnouncementEmail(userIds, title, message, actionUrl = null) {
    try {
      const users = await User.find({ 
        _id: { $in: userIds },
        email: { $exists: true, $ne: null }
      }).select('name email notificationPreferences');

      for (const user of users) {
        // Check if user wants email notifications for system announcements
        const emailPrefs = user.notificationPreferences?.systemAnnouncement;
        if (!emailPrefs?.email) continue;

        const emailData = createSystemAnnouncementEmail({
          recipientName: user.name,
          title,
          message,
          actionUrl
        });

        await sendEmail({
          to: user.email,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text
        });

        console.log(`✅ System announcement email sent to ${user.email}`);
      }
    } catch (error) {
      console.error('❌ Error sending system announcement emails:', error.message);
    }
  }

  // Send welcome email to new users
  static async sendWelcomeEmail(userId) {
    try {
      const user = await User.findById(userId).select('name email');
      if (!user || !user.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/dashboard`;
      
      const emailData = createSystemAnnouncementEmail({
        recipientName: user.name,
        title: 'Welcome to Task Manager Pro! 🎉',
        message: `Welcome to Task Manager Pro, ${user.name}!\n\nWe're excited to have you on board. Task Manager Pro helps you organize your tasks, collaborate with friends, and achieve your goals with our gamification system.\n\nHere's what you can do:\n• Create and manage tasks\n• Collaborate with friends\n• Earn badges and level up\n• Track your progress\n\nGet started by exploring your dashboard and creating your first task!`,
        actionUrl
      });

      await sendEmail({
        to: user.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Welcome email sent to ${user.email}`);
    } catch (error) {
      console.error('❌ Error sending welcome email:', error.message);
    }
  }

  // Send welcome email to new users
  static async sendWelcomeEmail(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const emailData = createSystemAnnouncementEmail({
        recipientName: user.name,
        title: 'Welcome to Task Manager Pro! 🎉',
        message: `Welcome to Task Manager Pro, ${user.name}!\n\nYour account has been created successfully and you're ready to start managing your tasks efficiently.\n\n🔔 Email Notifications Enabled:\n• Badge achievements and level ups\n• Task assignments and updates\n• System announcements\n\n🎮 Gamification Features:\n• Earn badges for completing tasks\n• Level up by gaining experience points\n• Complete missions to unlock achievements\n\nGet started by creating your first task and completing your profile to earn your first badges!\n\nYou can manage your notification preferences anytime in your account settings.`,
        actionUrl: `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/dashboard`
      });

      const result = await sendEmail({
        to: user.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      return result;
    } catch (error) {
      console.error('❌ Error sending welcome email:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Send subtask submission email notification
  static async sendSubtaskSubmissionNotification(taskData, subtaskData, submitterId, collaboratorIds) {
    try {
      const submitter = await User.findById(submitterId).select('name email');
      if (!submitter) return;

      // Get collaborators (excluding the submitter)
      const collaborators = await User.find({
        _id: { $in: collaboratorIds },
        _id: { $ne: submitterId }, // Exclude submitter
        email: { $exists: true, $ne: null }
      }).select('name email notificationPreferences');

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/task/${taskData._id}`;

      for (const collaborator of collaborators) {
        // Check if user wants email notifications for subtask submissions
        const emailPrefs = collaborator.notificationPreferences?.subtaskSubmission;
        if (!emailPrefs?.email) continue;

        const emailData = createSubtaskSubmissionEmail({
          recipientName: collaborator.name,
          submitterName: submitter.name,
          taskTitle: taskData.title,
          subtaskText: subtaskData.text,
          hasEvidence: subtaskData.evidence && subtaskData.evidence.length > 0,
          evidenceCount: subtaskData.evidence?.length || 0,
          actionUrl
        });

        await sendEmail({
          to: collaborator.email,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text
        });

        console.log(`✅ Subtask submission email sent to ${collaborator.email}`);
      }
    } catch (error) {
      console.error('Error sending subtask submission emails:', error);
    }
  }

  // Send subtask confirmation email notification
  static async sendSubtaskConfirmationNotification(taskData, subtaskData, confirmerId, originalSubmitterId) {
    try {
      const confirmer = await User.findById(confirmerId).select('name email');
      const originalSubmitter = await User.findById(originalSubmitterId).select('name email notificationPreferences');

      if (!confirmer || !originalSubmitter || !originalSubmitter.email) return;

      // Check if original submitter wants email notifications for confirmations
      const emailPrefs = originalSubmitter.notificationPreferences?.subtaskConfirmation;
      if (!emailPrefs?.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/task/${taskData._id}`;

      const emailData = createSubtaskConfirmationEmail({
        recipientName: originalSubmitter.name,
        confirmerName: confirmer.name,
        taskTitle: taskData.title,
        subtaskText: subtaskData.text,
        actionUrl
      });

      await sendEmail({
        to: originalSubmitter.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Subtask confirmation email sent to ${originalSubmitter.email}`);
    } catch (error) {
      console.error('Error sending subtask confirmation email:', error);
    }
  }

  // Send subtask rejection email notification
  static async sendSubtaskRejectionNotification(taskData, subtaskData, rejectorId, originalSubmitterId, reason = '') {
    try {
      const rejector = await User.findById(rejectorId).select('name email');
      const originalSubmitter = await User.findById(originalSubmitterId).select('name email notificationPreferences');

      if (!rejector || !originalSubmitter || !originalSubmitter.email) return;

      // Check if original submitter wants email notifications for rejections
      const emailPrefs = originalSubmitter.notificationPreferences?.subtaskRejection;
      if (!emailPrefs?.email) return;

      const actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/task/${taskData._id}`;

      const emailData = createSubtaskRejectionEmail({
        recipientName: originalSubmitter.name,
        rejectorName: rejector.name,
        taskTitle: taskData.title,
        subtaskText: subtaskData.text,
        reason,
        actionUrl
      });

      await sendEmail({
        to: originalSubmitter.email,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      console.log(`✅ Subtask rejection email sent to ${originalSubmitter.email}`);
    } catch (error) {
      console.error('Error sending subtask rejection email:', error);
    }
  }

  // Send test email
  static async sendTestEmail(recipientEmail, recipientName = 'User') {
    try {
      const emailData = createSystemAnnouncementEmail({
        recipientName,
        title: 'Email Configuration Test ✅',
        message: 'Congratulations! Your Task Manager Pro email notification system is working perfectly.\n\nYou will now receive email notifications for:\n• Task assignments and updates\n• Task completions\n• Friend requests\n• Badge achievements\n• System announcements\n\nYou can manage your email notification preferences in your account settings.',
        actionUrl: `${process.env.FRONTEND_URL || 'http://localhost:5174'}/user/dashboard`
      });

      const result = await sendEmail({
        to: recipientEmail,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text
      });

      return result;
    } catch (error) {
      console.error('❌ Error sending test email:', error);
      throw error;
    }
  }
}

export default EmailService;
