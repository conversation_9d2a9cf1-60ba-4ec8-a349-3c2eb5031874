import mongoose from 'mongoose';
import User from './models/User.js';
import Task from './models/Task.js';
import Notification from './models/Notification.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const createTestNotifications = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Find a user to create notifications for
    const user = await User.findOne();
    if (!user) {
      console.log('❌ No users found in database');
      return;
    }
    
    // Find a task to reference
    const task = await Task.findOne();
    
    console.log('👤 Creating notifications for user:', user.name);
    
    // Create different types of notifications with action buttons
    const notifications = [
      {
        recipient: user._id,
        type: 'SUBTASK_SUBMITTED',
        title: 'Subtask Submitted for Review',
        message: 'Neymar submitted subtask "Task 3" in "Task 3" and needs your confirmation',
        priority: 'HIGH',
        relatedTask: task?._id,
        actions: [
          {
            label: 'Review & Confirm',
            action: 'REVIEW_AND_CONFIRM',
            style: 'PRIMARY'
          },
          {
            label: 'Reject',
            action: 'REJECT',
            style: 'DANGER'
          }
        ]
      },
      {
        recipient: user._id,
        type: 'XP_GAINED',
        title: 'XP Gained',
        message: 'You earned 10 XP points!',
        priority: 'MEDIUM',
        actions: [
          {
            label: 'View Gallery',
            action: 'VIEW_GALLERY',
            style: 'PRIMARY'
          }
        ]
      },
      {
        recipient: user._id,
        type: 'TASK_ASSIGNED',
        title: 'New Task Assigned',
        message: 'You have been assigned a new task: "Complete Project Documentation"',
        priority: 'HIGH',
        relatedTask: task?._id,
        actions: [
          {
            label: 'View Task',
            action: 'VIEW_TASK',
            style: 'PRIMARY'
          },
          {
            label: 'View All Tasks',
            action: 'VIEW_TASKS',
            style: 'SECONDARY'
          }
        ]
      },
      {
        recipient: user._id,
        type: 'BADGE_EARNED',
        title: 'Badge Earned!',
        message: 'Congratulations! You earned the "Task Master" badge for completing 10 tasks.',
        priority: 'MEDIUM',
        actions: [
          {
            label: 'View Gallery',
            action: 'VIEW_GALLERY',
            style: 'PRIMARY'
          },
          {
            label: 'View Dashboard',
            action: 'VIEW_DASHBOARD',
            style: 'SECONDARY'
          }
        ]
      },
      {
        recipient: user._id,
        type: 'FRIEND_REQUEST_SENT',
        title: 'Collaboration Request Received',
        message: 'John Doe sent you a collaboration request',
        priority: 'MEDIUM',
        actions: [
          {
            label: 'Accept',
            action: 'ACCEPT_FRIEND_REQUEST',
            style: 'PRIMARY'
          },
          {
            label: 'Decline',
            action: 'DECLINE_FRIEND_REQUEST',
            style: 'DANGER'
          }
        ]
      }
    ];
    
    // Create notifications
    for (const notificationData of notifications) {
      const notification = await Notification.createNotification(notificationData);
      console.log('✅ Created notification:', notification.title);
    }
    
    console.log('\n🎉 Test notifications created successfully!');
    console.log('📱 Check your notifications page to test the action buttons');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
};

createTestNotifications();
