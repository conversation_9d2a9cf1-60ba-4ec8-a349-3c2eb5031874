import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../../components/layouts/DashboardLayout';
import axiosInstance from '../../utils/axiosInstance';
import { API_PATHS } from '../../utils/apiPaths';
import { 
  LuBell, 
  LuBellRing, 
  LuCheck, 
  LuCheckCheck, 
  LuTrash2, 
  LuRefreshCw, 
  LuFilter,
  Lu<PERSON>ye,
  Lu<PERSON>ye<PERSON>,
  Lu<PERSON>lock,
  LuUser,
  LuCalendar,
  LuArrowRight
} from 'react-icons/lu';
import { toast } from 'react-hot-toast';
import moment from 'moment';

const Notifications = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'read', 'unread'
  const [stats, setStats] = useState({ total: 0, read: 0, unread: 0 });
  const [selectedNotifications, setSelectedNotifications] = useState(new Set());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const params = {
        limit: 50,
        page: 1,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      if (filter === 'unread') params.isRead = false;
      if (filter === 'read') params.isRead = true;

      const response = await axiosInstance.get(API_PATHS.NOTIFICATIONS.GET_NOTIFICATIONS, { params });
      setNotifications(response.data.notifications || []);
      
      // Fetch stats
      const statsResponse = await axiosInstance.get(API_PATHS.NOTIFICATIONS.GET_NOTIFICATION_STATS);
      setStats({
        total: statsResponse.data.total || 0,
        read: statsResponse.data.read || 0,
        unread: statsResponse.data.unread || 0
      });
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  // Refresh notifications
  const refreshNotifications = async () => {
    setIsRefreshing(true);
    await fetchNotifications();
    setIsRefreshing(false);
    toast.success('Notifications refreshed');
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      await axiosInstance.put(API_PATHS.NOTIFICATIONS.MARK_AS_READ(notificationId));
      setNotifications(prev => 
        prev.map(notif => 
          notif._id === notificationId 
            ? { ...notif, isRead: true }
            : notif
        )
      );
      setStats(prev => ({
        ...prev,
        read: prev.read + 1,
        unread: Math.max(0, prev.unread - 1)
      }));
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark as read');
    }
  };

  // Mark notification as unread
  const markAsUnread = async (notificationId) => {
    try {
      await axiosInstance.put(API_PATHS.NOTIFICATIONS.MARK_AS_UNREAD(notificationId));
      setNotifications(prev => 
        prev.map(notif => 
          notif._id === notificationId 
            ? { ...notif, isRead: false }
            : notif
        )
      );
      setStats(prev => ({
        ...prev,
        read: Math.max(0, prev.read - 1),
        unread: prev.unread + 1
      }));
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      toast.error('Failed to mark as unread');
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      await axiosInstance.put(API_PATHS.NOTIFICATIONS.MARK_ALL_READ);
      setNotifications(prev => prev.map(notif => ({ ...notif, isRead: true })));
      setStats(prev => ({
        ...prev,
        read: prev.total,
        unread: 0
      }));
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast.error('Failed to mark all as read');
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId) => {
    try {
      const notification = notifications.find(n => n._id === notificationId);
      await axiosInstance.delete(API_PATHS.NOTIFICATIONS.DELETE_NOTIFICATION(notificationId));
      
      setNotifications(prev => prev.filter(notif => notif._id !== notificationId));
      setStats(prev => ({
        total: prev.total - 1,
        read: notification?.isRead ? prev.read - 1 : prev.read,
        unread: !notification?.isRead ? Math.max(0, prev.unread - 1) : prev.unread
      }));
      toast.success('Notification deleted');
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('Failed to delete notification');
    }
  };

  // Clear all read notifications
  const clearReadNotifications = async () => {
    try {
      await axiosInstance.delete(API_PATHS.NOTIFICATIONS.CLEAR_READ_NOTIFICATIONS);
      setNotifications(prev => prev.filter(notif => !notif.isRead));
      setStats(prev => ({
        total: prev.unread,
        read: 0,
        unread: prev.unread
      }));
      toast.success('Read notifications cleared');
    } catch (error) {
      console.error('Error clearing read notifications:', error);
      toast.error('Failed to clear read notifications');
    }
  };

  // Get notification icon
  const getNotificationIcon = (type) => {
    const iconMap = {
      'TASK_ASSIGNED': '📋',
      'TASK_COMPLETED': '✅',
      'TASK_UPDATED': '📝',
      'TASK_OVERDUE': '⏰',
      'SUBTASK_SUBMITTED': '📎',
      'SUBTASK_CONFIRMED': '✅',
      'SUBTASK_REJECTED': '❌',
      'FRIEND_REQUEST_SENT': '👥',
      'FRIEND_REQUEST_ACCEPTED': '🤝',
      'BADGE_EARNED': '🏆',
      'LEVEL_UP': '⭐',
      'SYSTEM_ANNOUNCEMENT': '📢'
    };
    return iconMap[type] || '🔔';
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    const colorMap = {
      'LOW': 'text-gray-500 bg-gray-100',
      'MEDIUM': 'text-blue-500 bg-blue-100',
      'HIGH': 'text-orange-500 bg-orange-100',
      'URGENT': 'text-red-500 bg-red-100'
    };
    return colorMap[priority] || colorMap['MEDIUM'];
  };

  // Handle action button clicks
  const handleActionClick = (action, notification, e) => {
    e.stopPropagation();

    switch (action.action) {
      case 'VIEW_TASK':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'VIEW_GALLERY':
        navigate('/user/gallery');
        break;
      case 'REVIEW_CONFIRM':
      case 'REVIEW_AND_CONFIRM':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'REVISE_RESUBMIT':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'VIEW_EVIDENCE':
        if (notification.relatedTask) {
          navigate(`/user/task-details/${notification.relatedTask._id}`);
        }
        break;
      case 'ACCEPT_FRIEND_REQUEST':
        console.log('Accept friend request:', notification.relatedUser);
        break;
      case 'DECLINE_FRIEND_REQUEST':
        console.log('Decline friend request:', notification.relatedUser);
        break;
      case 'VIEW_PROFILE':
        if (notification.relatedUser) {
          navigate(`/users/${notification.relatedUser._id}`);
        }
        break;
      case 'VIEW_DASHBOARD':
        navigate('/user/dashboard');
        break;
      case 'VIEW_TASKS':
        navigate('/user/tasks');
        break;
      case 'CREATE_TASK':
        navigate('/user/create-task');
        break;
      default:
        console.log('Unknown action:', action.action);
    }

    // Mark notification as read when action is clicked
    if (!notification.isRead) {
      markAsRead(notification._id);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [filter]);

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.isRead;
    if (filter === 'read') return notification.isRead;
    return true;
  });

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <LuBell className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
                    <p className="text-sm text-gray-500">Stay updated with your latest activities</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <button
                    onClick={refreshNotifications}
                    disabled={isRefreshing}
                    className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    <LuRefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </button>
                  
                  {stats.unread > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <LuCheckCheck className="w-4 h-4" />
                      Mark All Read
                    </button>
                  )}
                  
                  {stats.read > 0 && (
                    <button
                      onClick={clearReadNotifications}
                      className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
                    >
                      <LuTrash2 className="w-4 h-4" />
                      Clear Read
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats and Filters */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white rounded-xl p-6 border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                  <LuBell className="w-5 h-5 text-gray-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  <p className="text-sm text-gray-500">Total Notifications</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <LuBellRing className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-600">{stats.unread}</p>
                  <p className="text-sm text-gray-500">Unread</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 border border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <LuCheck className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">{stats.read}</p>
                  <p className="text-sm text-gray-500">Read</p>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="bg-white rounded-xl border border-gray-200 mb-6">
            <div className="flex items-center gap-1 p-2">
              <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                {[
                  { key: 'all', label: 'All', icon: LuBell, count: stats.total },
                  { key: 'unread', label: 'Unread', icon: LuBellRing, count: stats.unread },
                  { key: 'read', label: 'Read', icon: LuCheck, count: stats.read }
                ].map(({ key, label, icon: Icon, count }) => (
                  <button
                    key={key}
                    onClick={() => setFilter(key)}
                    className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      filter === key
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {label}
                    {count > 0 && (
                      <span className={`px-2 py-0.5 text-xs rounded-full ${
                        filter === key ? 'bg-blue-100 text-blue-600' : 'bg-gray-200 text-gray-600'
                      }`}>
                        {count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="bg-white rounded-xl border border-gray-200">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading notifications...</span>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="text-center py-12 px-6">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <LuBell className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {filter === 'unread' ? 'No unread notifications' :
                   filter === 'read' ? 'No read notifications' :
                   'No notifications yet'}
                </h3>
                <p className="text-gray-500">
                  {filter === 'all' && "You'll see notifications here when you have new activity"}
                  {filter === 'unread' && "All caught up! No unread notifications."}
                  {filter === 'read' && "No read notifications to display."}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification._id}
                    className={`p-6 hover:bg-gray-50 transition-colors ${
                      !notification.isRead ? 'bg-blue-50/30 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start gap-4">
                      {/* Notification Icon */}
                      <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                        !notification.isRead ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <span className="text-lg">
                          {getNotificationIcon(notification.type)}
                        </span>
                      </div>

                      {/* Notification Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`text-sm font-semibold ${
                                !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                              }`}>
                                {notification.title}
                              </h3>
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              )}
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                getPriorityColor(notification.priority)
                              }`}>
                                {notification.priority}
                              </span>
                            </div>

                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>

                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <LuClock className="w-3 h-3" />
                                {moment(notification.createdAt).fromNow()}
                              </div>

                              {notification.sender && (
                                <div className="flex items-center gap-1">
                                  <LuUser className="w-3 h-3" />
                                  {notification.sender.name || 'System'}
                                </div>
                              )}

                              <div className="flex items-center gap-1">
                                <LuCalendar className="w-3 h-3" />
                                {moment(notification.createdAt).format('MMM D, YYYY')}
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center gap-2">
                            {!notification.isRead ? (
                              <button
                                onClick={() => markAsRead(notification._id)}
                                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Mark as read"
                              >
                                <LuEye className="w-4 h-4" />
                              </button>
                            ) : (
                              <button
                                onClick={() => markAsUnread(notification._id)}
                                className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                                title="Mark as unread"
                              >
                                <LuEyeOff className="w-4 h-4" />
                              </button>
                            )}

                            <button
                              onClick={() => deleteNotification(notification._id)}
                              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              title="Delete notification"
                            >
                              <LuTrash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        {/* Action Buttons from notification */}
                        {notification.actions && notification.actions.length > 0 && (
                          <div className="mt-3 flex items-center gap-2">
                            {notification.actions.map((action, index) => (
                              <button
                                key={index}
                                onClick={(e) => handleActionClick(action, notification, e)}
                                className={`inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                                  action.style === 'PRIMARY' ? 'bg-blue-600 text-white hover:bg-blue-700' :
                                  action.style === 'DANGER' ? 'bg-red-600 text-white hover:bg-red-700' :
                                  'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                              >
                                <LuArrowRight className="w-3 h-3" />
                                {action.label}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Notifications;
