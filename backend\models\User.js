import mongoose from "mongoose";

const UserSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    profileImageUrl: { type: String, default: null },
    role: { type: String, enum: ["member"], default: "member" },
    team: { type: mongoose.Schema.Types.ObjectId, ref: "Team", default: null }, // Link to Team
    friends: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],

    // Task references
    createdTasks: [{ type: mongoose.Schema.Types.ObjectId, ref: "Task" }], // Tasks created by this user
    assignedTasks: [{ type: mongoose.Schema.Types.ObjectId, ref: "Task" }], // Tasks assigned to this user

    // Gamification fields
    points: { type: Number, default: 0 },
    level: { type: Number, default: 1 },
    badges: { type: [String], default: [] },
    completedMissions: { type: [String], default: [] },

    // Notification preferences (added earlier)
    notificationPreferences: {
      taskAssigned: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: false },
      },
      taskCompleted: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: false },
        push: { type: Boolean, default: false },
      },
      taskOverdue: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: true },
      },
      taskDueSoon: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: false },
      },
      friendRequest: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      friendRequestAccepted: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      badgeEarned: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      levelUp: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      systemAnnouncement: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: false },
      },
      subtaskSubmission: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      subtaskConfirmation: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
      subtaskRejection: {
        inApp: { type: Boolean, default: true },
        email: { type: Boolean, default: true }, // ✅ ENABLED BY DEFAULT
        push: { type: Boolean, default: false },
      },
    },
  },
  { timestamps: true }
);

const User = mongoose.model("User", UserSchema);

export default User;
