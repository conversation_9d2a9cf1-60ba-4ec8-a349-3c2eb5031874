import express from "express";
import { completeMission, getUserGamificationData, cleanupUserBadges, fixUserXP, testGamificationNotifications, fixBadgeMissionAlignment } from "../controllers/gamificationController.js";
import { badges, missions } from "../data/gamificationData.js";
import { protect } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.get("/badges", (req, res) => {
  res.json(badges);
});

router.get("/missions", (req, res) => {
  res.json(missions);
});

// GET user gamification data (protected route)
router.get("/user-data", protect, getUserGamificationData);

// POST to cleanup user badges (protected route)
router.post("/cleanup-badges", protect, cleanupUserBadges);

// POST to fix user XP synchronization (protected route)
router.post("/fix-xp", protect, fixUserXP);

// POST to complete a mission (protected route)
router.post("/missions/complete/:id", protect, completeMission);

// POST to test gamification notifications (protected route)
router.post("/test-notifications", protect, testGamificationNotifications);

// POST to fix badge-mission alignment (protected route)
router.post("/fix-badge-mission-alignment", protect, fixBadgeMissionAlignment);

export default router;
