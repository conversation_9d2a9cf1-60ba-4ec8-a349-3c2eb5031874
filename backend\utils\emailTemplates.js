// Email template utilities for different notification types

// Base email template wrapper
const createBaseTemplate = (content, title = "Task Manager Pro") => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f5f5f7; }
        .container { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; border-left: 4px solid #667eea; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 500; margin: 10px 0; }
        .button:hover { background: #5a67d8; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .task-info { background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin: 15px 0; }
        .priority-high { border-left-color: #e53e3e; }
        .priority-medium { border-left-color: #dd6b20; }
        .priority-low { border-left-color: #38a169; }
        .status-completed { color: #38a169; }
        .status-pending { color: #dd6b20; }
        .status-in-progress { color: #3182ce; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📋 ${title}</h1>
        </div>
        <div class="content">
          ${content}
        </div>
        <div class="footer">
          <p>This email was sent by Task Manager Pro<br>
          <small>If you don't want to receive these emails, you can update your notification preferences in your account settings.</small></p>
        </div>
      </div>
    </body>
    </html>
  `;
};

// Task Assignment Email Template
export const createTaskAssignmentEmail = (data) => {
  const { recipientName, senderName, task, actionUrl } = data;
  
  const content = `
    <h2>🎯 New Task Assigned</h2>
    <p>Hi ${recipientName},</p>
    <p><strong>${senderName}</strong> has assigned you a new task:</p>
    
    <div class="task-info priority-${task.priority?.toLowerCase() || 'medium'}">
      <h3 style="margin-top: 0; color: #2d3748;">${task.title}</h3>
      ${task.description ? `<p style="color: #4a5568; margin: 10px 0;">${task.description}</p>` : ''}
      
      <div style="display: flex; gap: 20px; margin-top: 15px; flex-wrap: wrap;">
        <div><strong>Priority:</strong> <span class="priority-${task.priority?.toLowerCase() || 'medium'}">${task.priority || 'Medium'}</span></div>
        <div><strong>Status:</strong> <span class="status-${task.status?.toLowerCase().replace(' ', '-') || 'pending'}">${task.status || 'Pending'}</span></div>
        ${task.dueDate ? `<div><strong>Due Date:</strong> ${new Date(task.dueDate).toLocaleDateString()}</div>` : ''}
      </div>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">View Task Details</a>
    </div>
    
    <div class="card">
      <p style="margin: 0; color: #4a5568;">
        💡 <strong>Quick Tip:</strong> You can update the task status, add comments, and collaborate with your team directly from the task details page.
      </p>
    </div>
  `;
  
  return {
    subject: `📋 New Task Assigned: ${task.title}`,
    html: createBaseTemplate(content, "New Task Assignment"),
    text: `New Task Assigned: ${task.title}\n\nHi ${recipientName},\n\n${senderName} has assigned you a new task: "${task.title}"\n\nPriority: ${task.priority || 'Medium'}\nStatus: ${task.status || 'Pending'}\n${task.description ? `Description: ${task.description}\n` : ''}${task.dueDate ? `Due Date: ${new Date(task.dueDate).toLocaleDateString()}\n` : ''}\n\nView task details: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// Task Completion Email Template
export const createTaskCompletionEmail = (data) => {
  const { recipientName, completedByName, task, actionUrl } = data;
  
  const content = `
    <h2>✅ Task Completed</h2>
    <p>Hi ${recipientName},</p>
    <p>Great news! <strong>${completedByName}</strong> has completed a task:</p>
    
    <div class="task-info">
      <h3 style="margin-top: 0; color: #2d3748;">✅ ${task.title}</h3>
      ${task.description ? `<p style="color: #4a5568; margin: 10px 0;">${task.description}</p>` : ''}
      
      <div style="background: #f0fff4; border: 1px solid #9ae6b4; border-radius: 6px; padding: 12px; margin: 15px 0;">
        <p style="margin: 0; color: #276749;">
          🎉 <strong>Status:</strong> <span class="status-completed">Completed</span>
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">View Task Details</a>
    </div>
    
    <div class="card">
      <p style="margin: 0; color: #4a5568;">
        🏆 <strong>Well Done!</strong> Another task completed successfully. Keep up the great work!
      </p>
    </div>
  `;
  
  return {
    subject: `✅ Task Completed: ${task.title}`,
    html: createBaseTemplate(content, "Task Completed"),
    text: `Task Completed: ${task.title}\n\nHi ${recipientName},\n\nGreat news! ${completedByName} has completed the task: "${task.title}"\n\n${task.description ? `Description: ${task.description}\n` : ''}Status: Completed\n\nView task details: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// Friend Request Email Template
export const createFriendRequestEmail = (data) => {
  const { recipientName, senderName, senderEmail, actionUrl } = data;
  
  const content = `
    <h2>👋 New Collaboration Request</h2>
    <p>Hi ${recipientName},</p>
    <p><strong>${senderName}</strong> (${senderEmail}) wants to connect with you on Task Manager Pro!</p>

    <div class="card">
      <h3 style="margin-top: 0; color: #2d3748;">🤝 Connect and Collaborate</h3>
      <p style="color: #4a5568; margin: 10px 0;">
        By accepting this collaboration request, you'll be able to:
      </p>
      <ul style="color: #4a5568; line-height: 1.6;">
        <li>📋 Assign tasks to each other</li>
        <li>👥 Collaborate on projects</li>
        <li>📊 Share progress and achievements</li>
        <li>🏆 Compete in friendly challenges</li>
      </ul>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">View Collaboration Request</a>
    </div>
  `;

  return {
    subject: `👋 ${senderName} wants to connect with you`,
    html: createBaseTemplate(content, "New Collaboration Request"),
    text: `New Collaboration Request\n\nHi ${recipientName},\n\n${senderName} (${senderEmail}) wants to connect with you on Task Manager Pro!\n\nBy accepting this collaboration request, you'll be able to assign tasks to each other, collaborate on projects, and share achievements.\n\nView collaboration request: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// Badge Earned Email Template
export const createBadgeEarnedEmail = (data) => {
  const { recipientName, badgeName, badgeDescription, actionUrl } = data;
  
  const content = `
    <h2>🏆 Congratulations! Badge Earned</h2>
    <p>Hi ${recipientName},</p>
    <p>Amazing work! You've just earned a new badge:</p>
    
    <div style="text-align: center; margin: 30px 0; padding: 30px; background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); border-radius: 15px;">
      <div style="font-size: 48px; margin-bottom: 15px;">🏆</div>
      <h3 style="margin: 0; color: #744210; font-size: 24px;">${badgeName}</h3>
      ${badgeDescription ? `<p style="color: #975a16; margin: 10px 0; font-style: italic;">${badgeDescription}</p>` : ''}
    </div>
    
    <div class="card">
      <h3 style="margin-top: 0; color: #2d3748;">🎯 Keep Going!</h3>
      <p style="color: #4a5568; margin: 10px 0;">
        You're making excellent progress! Check out your profile to see all your achievements and discover what badges you can earn next.
      </p>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">View Your Gallery</a>
    </div>
  `;
  
  return {
    subject: `🏆 Badge Earned: ${badgeName}`,
    html: createBaseTemplate(content, "Badge Earned"),
    text: `Badge Earned: ${badgeName}\n\nHi ${recipientName},\n\nAmazing work! You've just earned a new badge: "${badgeName}"\n\n${badgeDescription ? `${badgeDescription}\n\n` : ''}Keep up the excellent work! Check out your profile to see all your achievements.\n\nView your gallery: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// System Announcement Email Template
export const createSystemAnnouncementEmail = (data) => {
  const { recipientName, title, message, actionUrl } = data;

  const content = `
    <h2>📢 ${title}</h2>
    <p>Hi ${recipientName},</p>

    <div class="card">
      <div style="color: #4a5568; line-height: 1.6;">
        ${message.split('\n').map(paragraph => `<p>${paragraph}</p>`).join('')}
      </div>
    </div>

    ${actionUrl ? `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${actionUrl}" class="button">Learn More</a>
      </div>
    ` : ''}
  `;

  return {
    subject: `📢 ${title}`,
    html: createBaseTemplate(content, title),
    text: `${title}\n\nHi ${recipientName},\n\n${message}\n\n${actionUrl ? `Learn more: ${actionUrl}\n\n` : ''}This email was sent by Task Manager Pro.`
  };
};

// Subtask Submission Email Template
export const createSubtaskSubmissionEmail = (data) => {
  const { recipientName, submitterName, taskTitle, subtaskText, hasEvidence, evidenceCount, actionUrl } = data;

  const content = `
    <h2>🔍 Subtask Submitted for Review</h2>
    <p>Hi ${recipientName},</p>
    <p><strong>${submitterName}</strong> has submitted a subtask for your review:</p>

    <div class="card">
      <h3 style="margin-top: 0; color: #2d3748;">📋 ${taskTitle}</h3>
      <p><strong>Subtask:</strong> ${subtaskText}</p>
      <p><strong>Evidence:</strong> ${hasEvidence ? `✅ ${evidenceCount} file(s) attached` : '❌ No evidence provided'}</p>
      <p><strong>Status:</strong> <span class="status-pending">⏳ Waiting for peer confirmation</span></p>
    </div>

    <p>Please review the submission and either confirm or reject it. The submitter cannot confirm their own work - your peer review is required.</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">Review & Confirm</a>
    </div>

    <div class="card">
      <p style="margin: 0; color: #4a5568;">
        🤝 <strong>Collaborative Task:</strong> This task requires peer confirmation to maintain quality and accountability.
      </p>
    </div>
  `;

  return {
    subject: `🔍 Subtask Submitted for Review - ${taskTitle}`,
    html: createBaseTemplate(content, "Subtask Review Required"),
    text: `Subtask Submitted for Review - ${taskTitle}\n\nHi ${recipientName},\n\n${submitterName} has submitted a subtask for your review:\n\nTask: ${taskTitle}\nSubtask: ${subtaskText}\nEvidence: ${hasEvidence ? `${evidenceCount} file(s) attached` : 'No evidence provided'}\nStatus: Waiting for peer confirmation\n\nPlease review and confirm or reject this submission: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// Subtask Confirmation Email Template
export const createSubtaskConfirmationEmail = (data) => {
  const { recipientName, confirmerName, taskTitle, subtaskText, actionUrl } = data;

  const content = `
    <h2>✅ Subtask Confirmed</h2>
    <p>Hi ${recipientName},</p>
    <p>Great news! <strong>${confirmerName}</strong> has confirmed your subtask submission:</p>

    <div class="card">
      <h3 style="margin-top: 0; color: #2d3748;">📋 ${taskTitle}</h3>
      <p><strong>Subtask:</strong> ${subtaskText}</p>
      <p><strong>Status:</strong> <span class="status-completed">✅ Peer Confirmed</span></p>
    </div>

    <p>Your work has been reviewed and approved. The subtask is now marked as completed!</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">View Task</a>
    </div>

    <div class="card">
      <p style="margin: 0; color: #4a5568;">
        🏆 <strong>Well Done!</strong> Your collaborative work has been validated by your peer. Keep up the excellent work!
      </p>
    </div>
  `;

  return {
    subject: `✅ Subtask Confirmed - ${taskTitle}`,
    html: createBaseTemplate(content, "Subtask Confirmed"),
    text: `Subtask Confirmed - ${taskTitle}\n\nHi ${recipientName},\n\nGreat news! ${confirmerName} has confirmed your subtask submission:\n\nTask: ${taskTitle}\nSubtask: ${subtaskText}\nStatus: Peer Confirmed\n\nYour work has been reviewed and approved. View task: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

// Subtask Rejection Email Template
export const createSubtaskRejectionEmail = (data) => {
  const { recipientName, rejectorName, taskTitle, subtaskText, reason, actionUrl } = data;

  const content = `
    <h2>🔄 Subtask Needs Revision</h2>
    <p>Hi ${recipientName},</p>
    <p><strong>${rejectorName}</strong> has requested revisions for your subtask submission:</p>

    <div class="card">
      <h3 style="margin-top: 0; color: #2d3748;">📋 ${taskTitle}</h3>
      <p><strong>Subtask:</strong> ${subtaskText}</p>
      <p><strong>Status:</strong> <span class="status-pending">🔄 Revision Requested</span></p>
      ${reason ? `<p><strong>Feedback:</strong> ${reason}</p>` : ''}
    </div>

    <p>Please review the feedback and resubmit your work when ready. This is part of the collaborative quality assurance process.</p>

    <div style="text-align: center; margin: 30px 0;">
      <a href="${actionUrl}" class="button">Revise & Resubmit</a>
    </div>

    <div class="card">
      <p style="margin: 0; color: #4a5568;">
        💡 <strong>Collaborative Feedback:</strong> This feedback helps ensure high-quality deliverables and continuous improvement.
      </p>
    </div>
  `;

  return {
    subject: `🔄 Subtask Revision Requested - ${taskTitle}`,
    html: createBaseTemplate(content, "Revision Requested"),
    text: `Subtask Revision Requested - ${taskTitle}\n\nHi ${recipientName},\n\n${rejectorName} has requested revisions for your subtask submission:\n\nTask: ${taskTitle}\nSubtask: ${subtaskText}\nStatus: Revision Requested\n${reason ? `Feedback: ${reason}\n` : ''}\nPlease review and resubmit: ${actionUrl}\n\nThis email was sent by Task Manager Pro.`
  };
};

export default {
  createTaskAssignmentEmail,
  createTaskCompletionEmail,
  createFriendRequestEmail,
  createBadgeEarnedEmail,
  createSystemAnnouncementEmail
};
