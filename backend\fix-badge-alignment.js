import mongoose from 'mongoose';
import User from './models/User.js';
import { missions, badges } from './data/gamificationData.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const fixBadgeAlignment = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Find all users
    const users = await User.find();
    console.log(`👥 Found ${users.length} users to check`);
    
    for (const user of users) {
      console.log(`\n🔧 Checking user: ${user.name}`);
      console.log('Current badges:', user.badges || []);
      console.log('Current completed missions:', user.completedMissions || []);
      
      let badgesAdded = [];
      let missionsAdded = [];
      let changed = false;
      
      // Initialize arrays if they don't exist
      if (!user.badges) user.badges = [];
      if (!user.completedMissions) user.completedMissions = [];
      
      // Check each mission and ensure corresponding badge is awarded
      if (user.completedMissions.length > 0) {
        for (const missionIdStr of user.completedMissions) {
          const missionId = parseInt(missionIdStr);
          const mission = missions.find(m => m.id === missionId);
          
          if (mission && mission.badgeName) {
            const badge = badges.find(b => b.name === mission.badgeName);
            
            if (badge && !user.badges.includes(badge.name)) {
              user.badges.push(badge.name);
              badgesAdded.push(badge.name);
              changed = true;
              console.log(`  ✅ Added missing badge: ${badge.name}`);
            }
          }
        }
      }
      
      // Check each badge and ensure corresponding mission is completed
      if (user.badges.length > 0) {
        for (const badgeName of user.badges) {
          const mission = missions.find(m => m.badgeName === badgeName);
          
          if (mission) {
            const missionIdStr = mission.id.toString();
            if (!user.completedMissions.includes(missionIdStr)) {
              user.completedMissions.push(missionIdStr);
              missionsAdded.push(mission.title);
              changed = true;
              console.log(`  ✅ Added missing mission completion: ${mission.title}`);
            }
          }
        }
      }
      
      // Remove duplicates
      const originalBadgeCount = user.badges.length;
      const originalMissionCount = user.completedMissions.length;
      
      user.badges = [...new Set(user.badges)];
      user.completedMissions = [...new Set(user.completedMissions)];
      
      if (user.badges.length !== originalBadgeCount || user.completedMissions.length !== originalMissionCount) {
        changed = true;
        console.log(`  🧹 Removed duplicates`);
      }
      
      // Save changes if any were made
      if (changed) {
        await user.save();
        console.log(`  💾 Saved changes for ${user.name}`);
        console.log(`  📊 Final state: ${user.badges.length} badges, ${user.completedMissions.length} missions`);
      } else {
        console.log(`  ✨ No changes needed for ${user.name}`);
      }
    }
    
    console.log('\n🎉 Badge-mission alignment fix completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
};

fixBadgeAlignment();
