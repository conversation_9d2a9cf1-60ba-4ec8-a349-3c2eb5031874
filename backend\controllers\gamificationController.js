import { missions, badges } from "../data/gamificationData.js";
import User from "../models/User.js"; // assuming you use Mongoose
import Task from "../models/Task.js";
import NotificationService from "../utils/notificationService.js";

// Test endpoint to check user data
export const getUserGamificationData = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        points: user.points || 0,
        level: user.level || 1,
        badges: user.badges || [],
        completedMissions: user.completedMissions || []
      },
      availableMissions: missions.slice(0, 5), // First 5 missions for testing
      availableBadges: badges.slice(0, 5)
    });
  } catch (err) {
    console.error("Get gamification data error:", err);
    res.status(500).json({ success: false, message: "Server error", error: err.message });
  }
};

// Cleanup endpoint to fix duplicate badges
export const cleanupUserBadges = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    const originalBadgeCount = user.badges?.length || 0;
    const originalMissionCount = user.completedMissions?.length || 0;

    // Remove duplicates from badges and missions
    user.badges = [...new Set(user.badges || [])];
    user.completedMissions = [...new Set(user.completedMissions || [])];

    // Force save to ensure changes persist
    await user.save();

    console.log('Cleanup completed:', {
      originalBadges: originalBadgeCount,
      newBadges: user.badges.length,
      originalMissions: originalMissionCount,
      newMissions: user.completedMissions.length
    });

    res.json({
      success: true,
      message: "Badges and missions cleaned up successfully",
      originalBadgeCount: originalBadgeCount,
      newBadgeCount: user.badges.length,
      originalMissionCount: originalMissionCount,
      newMissionCount: user.completedMissions.length,
      badges: user.badges,
      completedMissions: user.completedMissions
    });
  } catch (err) {
    console.error("Cleanup badges error:", err);
    res.status(500).json({ success: false, message: "Server error", error: err.message });
  }
};

// Fix XP synchronization issues
export const fixUserXP = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    // Calculate total XP from completed missions
    let totalMissionXP = 0;
    if (user.completedMissions && user.completedMissions.length > 0) {
      user.completedMissions.forEach(missionId => {
        const mission = missions.find(m => m.id === parseInt(missionId));
        if (mission) {
          totalMissionXP += mission.points;
        }
      });
    }

    // Get completed tasks count for task XP (10 XP per completed task)
    const Task = (await import('../models/Task.js')).default;
    const completedTasksCount = await Task.countDocuments({
      assignedTo: user._id,
      status: "Completed"
    });
    const taskXP = completedTasksCount * 10;

    // Calculate correct total XP
    const correctTotalXP = totalMissionXP + taskXP;

    // Update user's points and level
    user.points = correctTotalXP;
    user.level = Math.floor(correctTotalXP / 100) + 1;

    // Clean up duplicate badges and missions
    user.badges = [...new Set(user.badges || [])];
    user.completedMissions = [...new Set(user.completedMissions || [])];

    await user.save();

    res.json({
      success: true,
      message: "User XP synchronized successfully",
      data: {
        missionXP: totalMissionXP,
        taskXP: taskXP,
        totalXP: correctTotalXP,
        level: user.level,
        completedMissions: user.completedMissions.length,
        completedTasks: completedTasksCount
      }
    });
  } catch (err) {
    console.error("Fix user XP error:", err);
    res.status(500).json({ success: false, message: "Server error", error: err.message });
  }
};

export const completeMission = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const missionId = parseInt(req.params.id);

    console.log('Completing mission:', { userId, missionId, userObject: req.user });

    if (!userId) {
      console.log('No user ID found in request');
      return res.status(401).json({ success: false, message: "User not authenticated" });
    }

    const mission = missions.find(m => m.id === missionId);
    console.log('Found mission:', mission);

    if (!mission) {
      console.log('Mission not found for ID:', missionId);
      return res.status(404).json({ success: false, message: "Mission not found" });
    }

    const user = await User.findById(userId);
    console.log('Found user:', { id: user?._id, points: user?.points, completedMissions: user?.completedMissions });

    if (!user) {
      console.log('User not found in database:', userId);
      return res.status(404).json({ success: false, message: "User not found" });
    }

    // Initialize arrays if they don't exist
    if (!user.completedMissions) {
      user.completedMissions = [];
    }
    if (!user.badges) {
      user.badges = [];
    }
    if (!user.points) {
      user.points = 0;
    }
    if (!user.level) {
      user.level = 1;
    }

    // Check if mission already completed (check both string and number formats)
    const missionIdStr = missionId.toString();
    if (user.completedMissions.includes(missionIdStr) || user.completedMissions.includes(missionId)) {
      console.log('Mission already completed:', missionId);
      return res.status(400).json({ success: false, message: "Mission already completed" });
    }

    // Award XP
    user.points += mission.points;

    // Mark mission as completed
    user.completedMissions.push(missionId.toString());

    // Award Badge if linked and not already owned
    const badge = badges.find(b => b.name === mission?.badgeName);
    let badgeAwarded = null;

    if (badge) {
      // Ensure badges array exists and remove duplicates first
      user.badges = [...new Set(user.badges || [])];

      if (!user.badges.includes(badge.name)) {
        user.badges.push(badge.name);
        badgeAwarded = badge.name;
        console.log('Badge awarded:', badge.name);
      } else {
        console.log('Badge already owned:', badge.name);
      }
    }

    // Update level based on points (every 100 points = 1 level)
    const oldLevel = user.level;
    const newLevel = Math.floor(user.points / 100) + 1;
    user.level = newLevel;

    await user.save();

    // Create notifications for gamification events
    try {
      // Mission completed notification
      await NotificationService.createGamificationNotification(userId, "MISSION_COMPLETED", {
        missionTitle: mission.title,
        missionDescription: mission.description,
        pointsAwarded: mission.points,
        totalPoints: user.points
      });

      // Badge earned notification
      if (badgeAwarded) {
        await NotificationService.createGamificationNotification(userId, "BADGE_EARNED", {
          badgeName: badgeAwarded,
          badgeDescription: badge.description,
          badgeRarity: badge.rarity
        });
      }

      // Level up notification
      if (newLevel > oldLevel) {
        await NotificationService.createGamificationNotification(userId, "LEVEL_UP", {
          oldLevel: oldLevel,
          newLevel: newLevel,
          totalPoints: user.points
        });
      }

      console.log('Gamification notifications created successfully');
    } catch (notificationError) {
      console.error('Error creating gamification notifications:', notificationError);
      // Don't fail the mission completion if notification creation fails
    }

    console.log('Mission completed successfully:', {
      points: user.points,
      level: user.level,
      badgeAwarded: badge?.name
    });

    res.json({
      success: true,
      message: "Mission completed",
      badgeAwarded: badgeAwarded,
      pointsAwarded: mission.points,
      newLevel: user.level,
      totalPoints: user.points
    });
  } catch (err) {
    console.error("Complete mission error:", err);
    console.error("Error stack:", err.stack);
    res.status(500).json({ success: false, message: "Server error", error: err.message });
  }
};

// Test endpoint to manually trigger gamification notifications
export const testGamificationNotifications = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;

    if (!userId) {
      return res.status(401).json({ success: false, message: "User not authenticated" });
    }

    // Test different types of gamification notifications
    const testNotifications = [
      {
        type: "BADGE_EARNED",
        data: {
          badgeName: "Test Badge",
          badgeDescription: "This is a test badge notification",
          badgeRarity: "common"
        }
      },
      {
        type: "LEVEL_UP",
        data: {
          oldLevel: 1,
          newLevel: 2,
          totalPoints: 150
        }
      },
      {
        type: "MISSION_COMPLETED",
        data: {
          missionTitle: "Test Mission",
          missionDescription: "This is a test mission notification",
          pointsAwarded: 25,
          totalPoints: 175
        }
      },
      {
        type: "XP_GAINED",
        data: {
          xpAmount: 10,
          totalPoints: 185,
          source: "Test Action"
        }
      }
    ];

    const results = [];

    for (const notification of testNotifications) {
      try {
        await NotificationService.createGamificationNotification(userId, notification.type, notification.data);
        results.push({ type: notification.type, status: "success" });
      } catch (error) {
        results.push({ type: notification.type, status: "error", error: error.message });
      }
    }

    res.json({
      success: true,
      message: "Test notifications created",
      results: results
    });
  } catch (err) {
    console.error("Test gamification notifications error:", err);
    res.status(500).json({ success: false, message: "Server error", error: err.message });
  }
};

// Fix badge-mission alignment issues
export const fixBadgeMissionAlignment = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    console.log('Fixing badge-mission alignment for user:', user.name);
    console.log('Current badges:', user.badges);
    console.log('Current completed missions:', user.completedMissions);

    let badgesAdded = [];
    let missionsAdded = [];

    // Check each mission and ensure corresponding badge is awarded
    if (user.completedMissions && user.completedMissions.length > 0) {
      for (const missionIdStr of user.completedMissions) {
        const missionId = parseInt(missionIdStr);
        const mission = missions.find(m => m.id === missionId);

        if (mission && mission.badgeName) {
          const badge = badges.find(b => b.name === mission.badgeName);

          if (badge && !user.badges.includes(badge.name)) {
            user.badges.push(badge.name);
            badgesAdded.push(badge.name);
            console.log('Added missing badge:', badge.name);
          }
        }
      }
    }

    // Check each badge and ensure corresponding mission is completed
    if (user.badges && user.badges.length > 0) {
      for (const badgeName of user.badges) {
        const badge = badges.find(b => b.name === badgeName);
        const mission = missions.find(m => m.badgeName === badgeName);

        if (mission) {
          const missionIdStr = mission.id.toString();
          if (!user.completedMissions.includes(missionIdStr)) {
            user.completedMissions.push(missionIdStr);
            missionsAdded.push(mission.title);
            console.log('Added missing mission completion:', mission.title);
          }
        }
      }
    }

    // Remove duplicates
    user.badges = [...new Set(user.badges || [])];
    user.completedMissions = [...new Set(user.completedMissions || [])];

    await user.save();

    res.json({
      success: true,
      message: "Badge-mission alignment fixed successfully",
      data: {
        badgesAdded: badgesAdded,
        missionsAdded: missionsAdded,
        totalBadges: user.badges.length,
        totalCompletedMissions: user.completedMissions.length
      }
    });
  } catch (error) {
    console.error("Fix badge-mission alignment error:", error);
    res.status(500).json({ success: false, message: "Server error", error: error.message });
  }
};
