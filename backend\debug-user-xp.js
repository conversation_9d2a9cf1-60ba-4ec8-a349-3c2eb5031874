import mongoose from 'mongoose';
import User from './models/User.js';
import Task from './models/Task.js';
import { missions, badges } from './data/gamificationData.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const debugUserXP = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Find the first user to debug
    const user = await User.findOne();
    if (!user) {
      console.log('❌ No users found in database');
      return;
    }
    
    console.log(`\n🔍 Debugging user: ${user.name} (${user.email})`);
    console.log('📊 Current user data:');
    console.log('  Points:', user.points || 0);
    console.log('  Level:', user.level || 1);
    console.log('  Badges:', user.badges || []);
    console.log('  Completed Missions:', user.completedMissions || []);
    
    // Calculate expected XP from missions
    let totalMissionXP = 0;
    if (user.completedMissions && user.completedMissions.length > 0) {
      console.log('\n🎯 Mission XP breakdown:');
      user.completedMissions.forEach(missionId => {
        const mission = missions.find(m => m.id === parseInt(missionId));
        if (mission) {
          console.log(`  Mission ${mission.id}: ${mission.title} = ${mission.points} XP`);
          totalMissionXP += mission.points;
        } else {
          console.log(`  Mission ${missionId}: NOT FOUND`);
        }
      });
    }
    
    // Calculate expected XP from tasks
    const completedTasks = await Task.find({ 
      $or: [
        { createdBy: user._id, status: 'Completed' },
        { assignedTo: user._id, status: 'Completed' }
      ]
    });
    const taskXP = completedTasks.length * 10;
    
    console.log('\n📋 Task XP breakdown:');
    console.log(`  Completed tasks: ${completedTasks.length}`);
    console.log(`  Task XP (10 per task): ${taskXP} XP`);
    
    // Calculate totals
    const expectedTotalXP = totalMissionXP + taskXP;
    const expectedLevel = Math.floor(expectedTotalXP / 100) + 1;
    
    console.log('\n🧮 XP Calculation Summary:');
    console.log(`  Mission XP: ${totalMissionXP}`);
    console.log(`  Task XP: ${taskXP}`);
    console.log(`  Expected Total XP: ${expectedTotalXP}`);
    console.log(`  Expected Level: ${expectedLevel}`);
    console.log(`  Current XP: ${user.points || 0}`);
    console.log(`  Current Level: ${user.level || 1}`);
    
    // Check if fix is needed
    const needsXPFix = (user.points || 0) !== expectedTotalXP;
    const needsLevelFix = (user.level || 1) !== expectedLevel;
    
    if (needsXPFix || needsLevelFix) {
      console.log('\n🔧 FIXES NEEDED:');
      if (needsXPFix) {
        console.log(`  ❌ XP mismatch: ${user.points || 0} → ${expectedTotalXP}`);
      }
      if (needsLevelFix) {
        console.log(`  ❌ Level mismatch: ${user.level || 1} → ${expectedLevel}`);
      }
      
      // Apply fixes
      console.log('\n💾 Applying fixes...');
      user.points = expectedTotalXP;
      user.level = expectedLevel;
      await user.save();
      
      console.log('✅ User data updated successfully!');
    } else {
      console.log('\n✨ No fixes needed - user data is correct!');
    }
    
    // Check badge alignment
    console.log('\n🏆 Badge alignment check:');
    let badgeIssues = [];
    
    // Check if all completed missions have corresponding badges
    if (user.completedMissions && user.completedMissions.length > 0) {
      for (const missionIdStr of user.completedMissions) {
        const missionId = parseInt(missionIdStr);
        const mission = missions.find(m => m.id === missionId);
        
        if (mission && mission.badgeName) {
          const hasBadge = user.badges && user.badges.includes(mission.badgeName);
          if (!hasBadge) {
            badgeIssues.push(`Missing badge: ${mission.badgeName} for mission: ${mission.title}`);
          }
        }
      }
    }
    
    if (badgeIssues.length > 0) {
      console.log('  ❌ Badge issues found:');
      badgeIssues.forEach(issue => console.log(`    ${issue}`));
    } else {
      console.log('  ✅ All badges are correctly aligned with missions');
    }
    
    console.log('\n🎉 Debug completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
};

debugUserXP();
