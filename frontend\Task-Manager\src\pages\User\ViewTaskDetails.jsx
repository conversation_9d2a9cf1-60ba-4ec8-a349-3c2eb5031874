import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import AvatarGroup from "../../components/AvatarGroup";
import ActivitiesTimeline from "../../components/Activities/ActivitiesTimeline";
import moment from "moment";
import { LuSquareArrowOutUpRight, LuDownload, LuEye, LuArrowLeft, LuTrendingUp, LuUsers, LuClipboardList, LuMessageCircle, LuCheck, LuX, LuSend, LuPaperclip, LuImage, LuFileText, LuVideo, LuLink, LuUpload, LuClock } from "react-icons/lu";
import { toast } from "react-hot-toast";
import FileUploadService from "../../services/fileUploadService";
import { useNotifications } from "../../contexts/NotificationContext";


const ViewTaskDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [activeTab, setActiveTab] = useState('details'); // 'details' or 'activities'
  const [pendingSubmissions, setPendingSubmissions] = useState(new Set()); // Track which subtasks are pending confirmation
  const [subtaskAttachments, setSubtaskAttachments] = useState({}); // Track attachments for each subtask
  const [showAttachmentModal, setShowAttachmentModal] = useState(null); // Track which subtask attachment modal is open

  // Modal form state (moved to component level to fix React Hooks violation)
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [linkUrl, setLinkUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const [currentUser, setCurrentUser] = useState(null);
  const { refreshNotifications } = useNotifications();

  const getStatusTagColor = (status) => {
    switch (status) {
      case "In Progress":
        return "text-cyan-500 bg-cyan-50 border border-cyan-500/10";
      case "Completed":
        return "text-lime-500 bg-lime-50 border border-lime-500/20";
      default:
        return "text-violet-500 bg-violet-50 border border-violet-500/10";
    }
  };

  const getTaskDetailsByID = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_TASK_BY_ID(id));
      if (response.data) {
        setTask(response.data);
      }
    } catch (error) {
      console.error("Error fetching task:", error);
    }
  };



  const getCurrentUser = async () => {
    try {
      const response = await axiosInstance.get('/api/auth/profile');
      if (response.data) {
        setCurrentUser(response.data);
      }
    } catch (error) {
      console.error("Error fetching current user:", error);
    }
  };



  // Check if task is collaborative (has assigned users)
  const isCollaborativeTask = () => {
    // Collaborative task = has assignedTo array with at least 1 user
    // Personal task = no assignedTo array or empty assignedTo array
    return task?.assignedTo && Array.isArray(task.assignedTo) && task.assignedTo.length > 0;
  };

  // Reset modal form state
  const resetModalState = () => {
    setSelectedFiles([]);
    setLinkUrl('');
    setIsUploading(false);
  };

  // Handle subtask submission (shows confirmation buttons or attachment modal for collaborative tasks)
  const handleSubtaskSubmit = (index) => {
    if (isCollaborativeTask()) {
      // For collaborative tasks, show attachment modal first
      resetModalState(); // Reset form state
      setShowAttachmentModal(index);
    } else {
      // For personal tasks, direct submission
      setPendingSubmissions(prev => new Set([...prev, index]));
    }
  };

  // Handle attachment upload for subtask
  const handleSubtaskAttachmentUpload = async (index, files, linkUrl = '') => {
    try {
      const attachments = [];

      // Handle file uploads to Firebase Storage
      if (files && files.length > 0) {
        toast.loading('Uploading files to Firebase...', { id: 'upload-toast' });

        for (const file of files) {
          try {
            // Validate file before upload
            FileUploadService.validateFile(file, 10); // 10MB limit

            // Upload to Firebase Storage with subtask-evidence folder
            const uploadedFile = await FileUploadService.uploadFile(file, 'subtask-evidence');

            attachments.push({
              name: uploadedFile.name,
              url: uploadedFile.url,
              size: uploadedFile.size,
              type: uploadedFile.type,
              fileName: uploadedFile.fileName,
              uploadedBy: currentUser?._id,
              uploadedAt: new Date()
            });
          } catch (fileError) {
            console.error(`Error uploading ${file.name}:`, fileError);
            toast.error(`Failed to upload ${file.name}: ${fileError.message}`);
          }
        }

        toast.dismiss('upload-toast');
      }

      // Handle link URL
      if (linkUrl.trim()) {
        // Basic URL validation
        try {
          new URL(linkUrl.trim());
          attachments.push({
            name: 'Link Evidence',
            url: linkUrl.trim(),
            type: 'link',
            uploadedBy: currentUser?._id,
            uploadedAt: new Date()
          });
        } catch (urlError) {
          toast.error('Please enter a valid URL');
          return;
        }
      }

      // Check if we have any attachments
      if (attachments.length === 0 && files && files.length > 0) {
        toast.error('No files were successfully uploaded');
        return;
      }

      // Store attachments for this subtask
      setSubtaskAttachments(prev => ({
        ...prev,
        [index]: attachments
      }));

      // Close modal and set as pending submission
      setShowAttachmentModal(null);
      resetModalState();
      setPendingSubmissions(prev => new Set([...prev, index]));

      // For collaborative tasks, update the task to track submission
      if (isCollaborativeTask()) {
        try {
          const updatedTodoChecklist = [...task.todoChecklist];
          updatedTodoChecklist[index] = {
            ...updatedTodoChecklist[index],
            submittedBy: currentUser?._id,
            submittedAt: new Date(),
            isPendingConfirmation: true,
            evidence: attachments
          };

          // Update the task in the database
          await axiosInstance.put(
            API_PATHS.TASKS.UPDATE_TODO_CHECKLIST(id),
            { todoChecklist: updatedTodoChecklist }
          );

          // Update local state to reflect the submission
          setTask(prev => ({
            ...prev,
            todoChecklist: updatedTodoChecklist
          }));

          // Force re-render by updating the pending submissions state
          setPendingSubmissions(prev => new Set([...prev, index]));

          // Send notification to collaborators about subtask submission
          try {
            await axiosInstance.post('/api/notifications/subtask-submission', {
              taskId: id,
              subtaskIndex: index,
              subtaskText: task.todoChecklist[index].text,
              hasEvidence: attachments.length > 0,
              evidenceCount: attachments.length
            });
          } catch (notificationError) {
            console.error('⚠️ Error sending submission notification:', notificationError);
            // Don't block the submission if notification fails
          }
        } catch (updateError) {
          console.error('❌ Error updating task:', updateError);
          toast.error('Failed to submit subtask. Please try again.');
          return; // Don't proceed with submission if update failed
        }
      }

      // Store attachments for this subtask
      setSubtaskAttachments(prev => ({
        ...prev,
        [index]: attachments
      }));

      // Close modal and set as pending submission
      setShowAttachmentModal(null);
      resetModalState();
      setPendingSubmissions(prev => new Set([...prev, index]));

      if (attachments.length > 0) {
        toast.success(`${attachments.length} evidence item(s) uploaded successfully! Waiting for peer confirmation.`);
      } else {
        toast.success('Subtask submitted without evidence. Waiting for peer confirmation.');
      }

    } catch (error) {
      console.error('❌ Error in subtask submission process:', error);
      toast.error(`Failed to submit subtask: ${error.response?.data?.message || error.message}`);
      toast.dismiss('upload-toast');
    }
  };

  // Handle subtask confirmation (approve submission)
  const handleSubtaskConfirm = async (index) => {
    const todoChecklist = [...task?.todoChecklist];
    const taskId = id;

    if (todoChecklist && todoChecklist[index]) {
      todoChecklist[index].completed = true;
      todoChecklist[index].completedBy = currentUser?._id;
      todoChecklist[index].completedAt = new Date();

      // Add evidence attachments if available
      if (subtaskAttachments[index]) {
        todoChecklist[index].evidence = subtaskAttachments[index];
      }

      // For collaborative tasks, track submission details
      if (isCollaborativeTask()) {
        todoChecklist[index].submittedBy = todoChecklist[index].submittedBy || currentUser?._id;
        todoChecklist[index].submittedAt = todoChecklist[index].submittedAt || new Date();
        todoChecklist[index].isPendingConfirmation = false; // Mark as confirmed
      }

      try {
        const response = await axiosInstance.put(
          API_PATHS.TASKS.UPDATE_TODO_CHECKLIST(taskId),
          { todoChecklist }
        );
        if (response.status === 200) {
          setTask(response.data?.task || task);
          setPendingSubmissions(prev => {
            const newSet = new Set(prev);
            newSet.delete(index);
            return newSet;
          });

          // Clear attachments for this subtask
          setSubtaskAttachments(prev => {
            const newAttachments = { ...prev };
            delete newAttachments[index];
            return newAttachments;
          });

          // Send confirmation notification for collaborative tasks
          if (isCollaborativeTask()) {
            try {
              await axiosInstance.post('/api/notifications/subtask-confirmation', {
                taskId: id,
                subtaskIndex: index,
                subtaskText: todoChecklist[index].text,
                originalSubmitterId: todoChecklist[index].submittedBy || currentUser?._id
              });
            } catch (notificationError) {
              console.error('Error sending confirmation notification:', notificationError);
            }
            toast.success('Subtask confirmed by peer! Marked as completed.');
          } else {
            toast.success('Subtask completed successfully!');
          }

          // Refresh notifications in case task completion triggered gamification notifications
          setTimeout(() => refreshNotifications(), 1000);
        } else {
          todoChecklist[index].completed = false;
          toast.error('Failed to update subtask');
        }
      } catch (error) {
        todoChecklist[index].completed = false;
        toast.error('Error updating subtask');
      }
    }
  };

  // Handle subtask rejection (cancel submission)
  const handleSubtaskReject = async (index) => {
    setPendingSubmissions(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });

    // Clear attachments for this subtask
    setSubtaskAttachments(prev => {
      const newAttachments = { ...prev };
      delete newAttachments[index];
      return newAttachments;
    });

    // Send rejection notification for collaborative tasks
    if (isCollaborativeTask()) {
      try {
        await axiosInstance.post('/api/notifications/subtask-rejection', {
          taskId: id,
          subtaskIndex: index,
          subtaskText: task.todoChecklist[index].text,
          originalSubmitterId: task.todoChecklist[index].submittedBy || currentUser?._id,
          reason: 'Submission was rejected by peer reviewer'
        });
      } catch (notificationError) {
        console.error('Error sending rejection notification:', notificationError);
      }
    }

    toast.info('Subtask submission cancelled');
  };

  // Render attachment modal for collaborative subtasks
  const renderAttachmentModal = (subtaskIndex) => {
    if (showAttachmentModal !== subtaskIndex) return null;

    // Handle file selection with validation
    const handleFileSelection = (e) => {
      const files = Array.from(e.target.files);
      const validFiles = [];

      files.forEach(file => {
        try {
          FileUploadService.validateFile(file, 10); // 10MB limit
          validFiles.push(file);
        } catch (error) {
          toast.error(`${file.name}: ${error.message}`);
        }
      });

      setSelectedFiles(validFiles);
    };

    // Handle upload with loading state
    const handleUpload = async (withEvidence = true) => {
      setIsUploading(true);
      try {
        if (withEvidence) {
          await handleSubtaskAttachmentUpload(subtaskIndex, selectedFiles, linkUrl);
        } else {
          await handleSubtaskAttachmentUpload(subtaskIndex, [], '');
        }
      } finally {
        setIsUploading(false);
      }
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        style={{ zIndex: 9999 }}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            setShowAttachmentModal(null);
            resetModalState();
          }
        }}
      >
        <div
          className="bg-white rounded-2xl p-6 max-w-lg w-full mx-4 shadow-2xl"
          style={{ zIndex: 10000 }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              📎 Add Evidence (Optional)
            </h3>
            <button
              onClick={() => {
                setShowAttachmentModal(null);
                resetModalState();
              }}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isUploading}
            >
              <LuX className="w-5 h-5" />
            </button>
          </div>

          <div className="space-y-4">
            {/* File Upload Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                📁 Upload Files
              </label>
              <div className="text-xs text-gray-500 mb-2">
                Supported: Images, PDFs, Videos, Documents (Max 10MB each)
              </div>
              <input
                type="file"
                multiple
                accept="image/*,application/pdf,video/*,.doc,.docx,.txt,.zip"
                onChange={handleFileSelection}
                disabled={isUploading}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"
              />
              {selectedFiles.length > 0 && (
                <div className="mt-2 p-2 bg-gray-50 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-1">
                    Selected Files ({selectedFiles.length}):
                  </div>
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="text-xs text-gray-600 flex items-center gap-2">
                      <LuPaperclip className="w-3 h-3" />
                      <span className="truncate">{file.name}</span>
                      <span className="text-gray-400">({(file.size / 1024 / 1024).toFixed(1)}MB)</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Link URL Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                🔗 Or Add Link Evidence
              </label>
              <input
                type="url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com/evidence"
                disabled={isUploading}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
              />
              <div className="text-xs text-gray-500 mt-1">
                Link to external evidence (Google Drive, YouTube, etc.)
              </div>
            </div>

            {/* Firebase Storage Info */}
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 text-sm text-blue-800">
                <LuUpload className="w-4 h-4" />
                <span className="font-medium">Secure Firebase Storage</span>
              </div>
              <div className="text-xs text-blue-600 mt-1">
                Files will be securely uploaded to your Firebase project: finalyearproject-c4f81
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={() => handleUpload(true)}
                disabled={isUploading || (selectedFiles.length === 0 && !linkUrl.trim())}
                className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {isUploading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <LuUpload className="w-4 h-4" />
                    Submit with Evidence
                  </>
                )}
              </button>
              <button
                onClick={() => handleUpload(false)}
                disabled={isUploading}
                className="flex-1 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Submit without Evidence
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };



  const handleEdit = () => {
    navigate(`/user/create-task?edit=${id}`);
  };

  const handleDelete = async () => {
    const confirmDelete = window.confirm("Are you sure you want to delete this task?");
    if (!confirmDelete) return;

    try {
      const response = await axiosInstance.delete(`/api/tasks/${id}`);
      if (response.status === 200) {
        alert("Task deleted successfully.");
        navigate("/user/tasks");
      }
    } catch (error) {
      console.error("Failed to delete task:", error);
      alert("Failed to delete task. Please try again.");
    }
  };

  useEffect(() => {
    if (id) {
      getTaskDetailsByID();
      getCurrentUser();
    }
  }, [id]);



  return (
    <DashboardLayout activeMenu="My Tasks">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Apple-style Navigation */}
        <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
          <div className="max-w-4xl mx-auto px-6 py-4">
            <button
              className="inline-flex items-center justify-center w-9 h-9 text-gray-600 hover:text-gray-900 hover:bg-gray-100/60 rounded-full transition-all duration-300 ease-out"
              onClick={() => navigate("/user/tasks")}
              title="Back to Tasks"
            >
              <LuArrowLeft className="w-4 h-4" />
            </button>
          </div>
        </div>

        {task && (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 overflow-hidden">
              {/* Apple-style Header */}
              <div className="p-8 pb-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <h1 className="text-3xl font-semibold text-gray-900 leading-tight mb-3">
                      {task?.title}
                    </h1>
                    <div className="flex items-center gap-3">
                      <div
                        className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${getStatusTagColor(
                          task?.status
                        )}`}
                      >
                        {task?.status}
                      </div>
                      <span className="text-gray-400">•</span>
                      <span className="text-sm text-gray-600">
                        Due {moment(task?.dueDate).format("MMM D, YYYY")}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 ml-6">
                    <button
                      onClick={handleEdit}
                      className="inline-flex items-center gap-2 px-4 py-2.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-xl transition-all duration-300 ease-out shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105"
                    >
                      ✏️ Edit
                    </button>

                    <button
                      onClick={handleDelete}
                      className="inline-flex items-center gap-2 px-4 py-2.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-xl transition-all duration-300 ease-out shadow-lg shadow-red-500/25 hover:shadow-red-500/40 hover:scale-105"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>

              {/* Tab Navigation - matching your reference design */}
              <div className="border-b border-gray-200/50 px-8">
                <div className="flex space-x-8">
                  <button
                    onClick={() => setActiveTab('details')}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'details'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <LuClipboardList className="w-4 h-4" />
                    Task Details
                  </button>
                  <button
                    onClick={() => setActiveTab('activities')}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'activities'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <LuMessageCircle className="w-4 h-4" />
                    Activities/Timeline
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              {activeTab === 'details' ? (
                <div className="px-8 space-y-6">
                  {/* Description Card */}
                  <div className="bg-gray-50/50 rounded-2xl p-6 border border-gray-100/50">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                    <p className="text-gray-700 leading-relaxed">{task?.description}</p>
                  </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-2xl p-6 border border-purple-200/30">
                    <h4 className="text-sm font-semibold text-purple-900 mb-2">Priority</h4>
                    <p className="text-lg font-medium text-purple-800">{task?.priority}</p>
                  </div>

                  <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-6 border border-blue-200/30">
                    <h4 className="text-sm font-semibold text-blue-900 mb-2">Due Date</h4>
                    <p className="text-lg font-medium text-blue-800">
                      {task?.dueDate ? moment(task?.dueDate).format("MMM D, YYYY") : "N/A"}
                    </p>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-2xl p-6 border border-purple-200/30">
                    <h4 className="text-sm font-semibold text-purple-900 mb-2">Created By</h4>
                    <div className="flex items-center gap-3">
                      <img
                        src={task?.createdBy?.profileImageUrl || "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiM4QjVDRjYiLz4KPHRleHQgeD0iMjAiIHk9IjI2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmb250LXdlaWdodD0iYm9sZCI+VTwvdGV4dD4KPHN2Zz4K"}
                        alt={task?.createdBy?.name || "User"}
                        className="w-10 h-10 rounded-full object-cover border-2 border-purple-200"
                        onError={(e) => {
                          // Use a data URL for fallback instead of external service
                          e.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iMjAiIGZpbGw9IiM4QjVDRjYiLz4KPHRleHQgeD0iMjAiIHk9IjI2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmb250LXdlaWdodD0iYm9sZCI+VTwvdGV4dD4KPHN2Zz4K";
                        }}
                      />
                      <div>
                        <p className="text-sm font-medium text-purple-900">
                          {task?.createdBy?.name || "Unknown"}
                        </p>
                        <p className="text-xs text-purple-600">
                          {task?.createdBy?.email || ""}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-2xl p-6 border border-green-200/30">
                    <h4 className="text-sm font-semibold text-green-900 mb-2">Assigned To</h4>
                    <AvatarGroup
                      avatars={
                        task?.assignedTo?.map((item) => item?.profileImageUrl) || []
                      }
                      maxVisible={5}
                    />
                  </div>
                </div>





                {/* Enhanced Subtask Management */}
              {task?.todoChecklist?.length > 0 && (
                <div className="px-8 mt-8 mb-8">
                  <div
                    className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden"
                    style={{
                      backgroundColor: '#ffffff',
                      border: '2px solid #e5e7eb',
                      borderRadius: '16px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      overflow: 'hidden',
                      marginBottom: '32px'
                    }}
                  >
                    {/* Header Section */}
                    <div
                      className="px-6 py-5 border-b border-gray-200 bg-gray-50"
                      style={{
                        backgroundColor: '#f9fafb',
                        borderBottom: '1px solid #e5e7eb',
                        padding: '20px 24px'
                      }}
                    >
                      <div>
                        <h3
                          className="text-lg font-semibold text-gray-900"
                          style={{
                            fontSize: '18px',
                            fontWeight: '600',
                            color: '#111827',
                            marginBottom: '4px'
                          }}
                        >
                          Task Checklist
                        </h3>
                        <p
                          className="text-sm text-gray-600"
                          style={{
                            fontSize: '14px',
                            color: '#6b7280',
                            margin: '0'
                          }}
                        >
                          {task.todoChecklist.filter(item => item.completed).length} of {task.todoChecklist.length} completed
                        </p>
                      </div>
                    </div>

                    {/* Content Section */}
                    <div
                      className="p-6"
                      style={{
                        backgroundColor: '#ffffff',
                        padding: '24px',
                        minHeight: '100px'
                      }}
                    >
                      <div
                        className="space-y-4"
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '16px'
                        }}
                      >
                        {task?.todoChecklist?.map((subtask, index) => {
                          const isPending = pendingSubmissions.has(index) || subtask.isPendingConfirmation;
                          const isCompleted = subtask.completed || false;
                          const isCollaborative = isCollaborativeTask();
                          const hasEvidence = subtaskAttachments[index] && subtaskAttachments[index].length > 0;

                          return (
                            <div key={`subtask_${index}`}>
                              <div
                                className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl transition-all duration-200 border border-gray-200 shadow-sm"
                                style={{
                                  backgroundColor: isCompleted ? '#f0fdf4' : isPending ? '#fef3c7' : '#f8fafc',
                                  border: `1px solid ${isCompleted ? '#bbf7d0' : isPending ? '#fcd34d' : '#e2e8f0'}`,
                                  borderRadius: '12px',
                                  padding: '16px 20px',
                                  display: 'flex',
                                  alignItems: 'flex-start',
                                  gap: '16px',
                                  minHeight: '56px',
                                  transition: 'all 0.2s ease'
                                }}
                              >
                                {/* Status Indicator */}
                                <div
                                  className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-1"
                                  style={{
                                    backgroundColor: isCompleted ? '#22c55e' : isPending ? '#f59e0b' : '#e5e7eb',
                                    width: '20px',
                                    height: '20px',
                                    minWidth: '20px',
                                    minHeight: '20px'
                                  }}
                                >
                                  {isCompleted && (
                                    <LuCheck className="w-3 h-3 text-white" />
                                  )}
                                  {isPending && (
                                    <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                                  )}
                                </div>

                                {/* Content Section */}
                                <div className="flex-1">
                                  {/* Subtask Text */}
                                  <div
                                    className={`text-base font-medium select-none ${
                                      isCompleted
                                        ? 'line-through text-gray-500'
                                        : 'text-gray-900'
                                    }`}
                                    style={{
                                      fontSize: '15px',
                                      lineHeight: '1.5',
                                      fontWeight: '500',
                                      userSelect: 'none',
                                      color: isCompleted ? '#6b7280' : '#111827',
                                      textDecoration: isCompleted ? 'line-through' : 'none'
                                    }}
                                  >
                                    {subtask.text}
                                  </div>

                                  {/* Task Type Indicator */}
                                  <div className="flex items-center gap-1 mt-1 text-xs">
                                    {isCollaborative ? (
                                      <div className="flex items-center gap-1 text-blue-600">
                                        <LuUsers className="w-3 h-3" />
                                        <span>
                                          Collaborative Task - Evidence submission with peer confirmation
                                          {task?.assignedTo?.length > 1 && ` (${task.assignedTo.length} collaborators)`}
                                        </span>
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-1 text-green-600">
                                        <LuClipboardList className="w-3 h-3" />
                                        <span>Personal Task - Direct completion</span>
                                      </div>
                                    )}
                                  </div>

                                  {/* Evidence Display */}
                                  {hasEvidence && (
                                    <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                      <div className="flex items-center gap-2 text-xs font-medium text-blue-800 mb-2">
                                        <LuPaperclip className="w-3 h-3" />
                                        Evidence Attached ({subtaskAttachments[index].length}):
                                      </div>
                                      <div className="space-y-2">
                                        {subtaskAttachments[index].map((attachment, attIndex) => (
                                          <div key={attIndex} className="flex items-center justify-between p-2 bg-white rounded border border-blue-200">
                                            <div className="flex items-center gap-2 flex-1 min-w-0">
                                              {attachment.type === 'link' ? (
                                                <LuLink className="w-3 h-3 text-blue-600 flex-shrink-0" />
                                              ) : attachment.type?.startsWith('image/') ? (
                                                <LuImage className="w-3 h-3 text-green-600 flex-shrink-0" />
                                              ) : attachment.type?.startsWith('video/') ? (
                                                <LuVideo className="w-3 h-3 text-purple-600 flex-shrink-0" />
                                              ) : (
                                                <LuFileText className="w-3 h-3 text-gray-600 flex-shrink-0" />
                                              )}
                                              <div className="flex-1 min-w-0">
                                                <div className="text-xs font-medium text-gray-900 truncate">
                                                  {attachment.name}
                                                </div>
                                                {attachment.size && (
                                                  <div className="text-xs text-gray-500">
                                                    {(attachment.size / 1024 / 1024).toFixed(1)}MB
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className="flex items-center gap-1 ml-2">
                                              <button
                                                onClick={() => window.open(attachment.url, '_blank')}
                                                className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors"
                                                title="View/Download"
                                              >
                                                <LuEye className="w-3 h-3" />
                                              </button>
                                              <button
                                                onClick={() => {
                                                  navigator.clipboard.writeText(attachment.url);
                                                  toast.success('Link copied to clipboard!');
                                                }}
                                                className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
                                                title="Copy Link"
                                              >
                                                <LuSquareArrowOutUpRight className="w-3 h-3" />
                                              </button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                      <div className="mt-2 text-xs text-blue-600">
                                        🔒 Stored securely in Firebase Storage
                                      </div>
                                    </div>
                                  )}

                                  {/* Pending Status for Collaborative Tasks */}
                                  {isPending && isCollaborative && (
                                    <div className="mt-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                      ⏳ Waiting for peer confirmation...
                                    </div>
                                  )}
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center gap-2">
                                  {!isCompleted && !isPending && (
                                    <button
                                      onClick={() => handleSubtaskSubmit(index)}
                                      className="flex items-center gap-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 text-sm font-medium"
                                      style={{
                                        backgroundColor: '#3b82f6',
                                        fontSize: '13px',
                                        padding: '6px 12px',
                                        borderRadius: '8px',
                                        fontWeight: '500'
                                      }}
                                    >
                                      <LuSend className="w-3 h-3" />
                                      {isCollaborative ? 'Submit Evidence' : 'Complete'}
                                    </button>
                                  )}

                                  {isPending && (
                                    <>
                                      {/* Check if current user can confirm (not the submitter) */}
                                      {subtask.submittedBy !== currentUser?._id ? (
                                        <>
                                          <button
                                            onClick={() => handleSubtaskConfirm(index)}
                                            className="flex items-center justify-center w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200"
                                            title="Confirm peer's submission"
                                            style={{
                                              backgroundColor: '#22c55e',
                                              width: '32px',
                                              height: '32px',
                                              borderRadius: '8px'
                                            }}
                                          >
                                            <LuCheck className="w-4 h-4" />
                                          </button>
                                          <button
                                            onClick={() => handleSubtaskReject(index)}
                                            className="flex items-center justify-center w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200"
                                            title="Reject submission"
                                            style={{
                                              backgroundColor: '#ef4444',
                                              width: '32px',
                                              height: '32px',
                                              borderRadius: '8px'
                                            }}
                                          >
                                            <LuX className="w-4 h-4" />
                                          </button>
                                        </>
                                      ) : (
                                        /* Show message for submitter */
                                        <div className="flex items-center gap-2 text-sm text-orange-600 mt-1">
                                          <LuClock className="w-4 h-4" />
                                          <span>Waiting for peer confirmation (you cannot confirm your own submission)</span>
                                        </div>
                                      )}
                                    </>
                                  )}

                                  {isCompleted && (
                                    <div className="text-sm text-green-600 font-medium">
                                      ✓ {isCollaborative ? 'Peer Confirmed' : 'Completed'}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Attachment Modal */}
                              {renderAttachmentModal(index)}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Apple-style Attachments */}
              {task?.attachments?.length > 0 && (
                <div className="px-8 mt-6 pb-8">
                  <div className="bg-white rounded-2xl border border-gray-200/50 shadow-sm overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-100">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <h3 className="text-lg font-semibold text-gray-900">Attachments</h3>
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-700">
                            {task.attachments.length}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {task.attachments.reduce((total, att) => total + (att.size || 0), 0) > 0 &&
                            `${FileUploadService.formatFileSize(
                              task.attachments.reduce((total, att) => total + (att.size || 0), 0)
                            )} total`
                          }
                        </div>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {task?.attachments?.map((attachment, index) => (
                          <AppleAttachment
                            key={`attachment_${index}`}
                            attachment={attachment}
                            index={index}
                            onFilePreview={setFilePreview}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
                </div>
              ) : (
                /* Activities/Timeline Tab */
                <div className="px-8 py-6">
                  <ActivitiesTimeline
                    taskId={id}
                    currentUser={currentUser}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Apple-style File Preview Modal */}
        {filePreview && (
          <FilePreviewModal
            file={filePreview}
            onClose={() => setFilePreview(null)}
          />
        )}
      </div>
    </DashboardLayout>
  );
};

// Text File Preview Component
const TextFilePreview = ({ url }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTextContent = async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('Failed to fetch file content');
        }
        const text = await response.text();
        setContent(text);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTextContent();
  }, [url]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
        <span className="ml-2 text-gray-600">Loading content...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <p>Error loading file: {error}</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono overflow-auto max-h-96">
        {content}
      </pre>
    </div>
  );
};

export default ViewTaskDetails;





// Apple-style attachment component
const AppleAttachment = ({ attachment, onFilePreview }) => {
  // Debug logging
  console.log('Attachment object:', attachment);

  // Check if attachment is a string (old format) or object (new format)
  const isFileObject = typeof attachment === 'object' && attachment.url;
  const isStringLink = typeof attachment === 'string';
  const isImage = isFileObject && attachment.type && attachment.type.startsWith('image/');
  const isPDF = isFileObject && attachment.type === 'application/pdf';
  const isVideo = isFileObject && attachment.type && attachment.type.startsWith('video/');
  const isAudio = isFileObject && attachment.type && attachment.type.startsWith('audio/');
  const isText = isFileObject && attachment.type && (
    attachment.type.startsWith('text/') ||
    attachment.type === 'application/json'
  );

  // Check if file can be previewed
  const canPreview = isImage || isPDF || isVideo || isAudio || isText;

  console.log('File type checks:', { isFileObject, isImage, isPDF, canPreview, url: attachment.url });

  const handleView = () => {
    if (canPreview && onFilePreview) {
      // Show file in modal preview
      onFilePreview(attachment);
    } else if (isFileObject && attachment.url) {
      window.open(attachment.url, '_blank');
    } else if (isStringLink) {
      let link = attachment;
      if (!/^https?:\/\//i.test(link)) {
        link = "https://" + link;
      }
      window.open(link, '_blank');
    }
  };

  const handleDownload = () => {
    if (isFileObject && attachment.url) {
      const link = document.createElement('a');
      link.href = attachment.url;
      link.download = attachment.name || 'download';
      link.click();
    }
  };

  const getFileIcon = () => {
    if (isFileObject && attachment.type) {
      return FileUploadService.getFileIcon(attachment.type);
    }
    return '🔗'; // Default link icon for string attachments
  };

  const getFileName = () => {
    if (isFileObject) {
      return attachment.name || 'Unknown file';
    }
    return attachment; // For string attachments, show the URL/link
  };

  const getFileSize = () => {
    if (isFileObject && attachment.size) {
      return FileUploadService.formatFileSize(attachment.size);
    }
    return null;
  };

  return (
    <div className="group relative bg-gradient-to-br from-white to-gray-50/30 border border-gray-200/60 rounded-2xl overflow-hidden hover:shadow-xl hover:shadow-gray-200/40 hover:border-gray-300/60 transition-all duration-500 ease-out cursor-pointer hover:scale-[1.02]">
      {/* Apple-style File Type Badge */}
      <div className="absolute top-4 right-4 z-10">
        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-white/90 text-gray-700 backdrop-blur-sm shadow-sm">
          {isFileObject && attachment.type ? attachment.type.split('/')[1].toUpperCase() : 'LINK'}
        </span>
      </div>

      {/* Image Thumbnail or Standard Content */}
      {isImage ? (
        <div className="p-5">
          <div className="flex items-start gap-5">
            {/* Image Thumbnail */}
            <div className="flex-shrink-0">
              <div className="relative group/image w-20 h-20 bg-gray-100 rounded-2xl overflow-hidden border border-gray-200 shadow-sm">
                {attachment.url ? (
                  <>
                    <img
                      src={attachment.url}
                      alt={attachment.name || 'Image'}
                      className="w-full h-full object-cover transition-all duration-300"
                      onClick={handleView}
                      onError={(e) => {
                        console.error('Image failed to load:', attachment.url);
                        e.target.style.display = 'none';
                        e.target.nextElementSibling.style.display = 'flex';
                      }}
                      onLoad={() => {
                        console.log('Image loaded successfully:', attachment.url);
                      }}
                    />
                    {/* Fallback when image fails to load */}
                    <div className="absolute inset-0 hidden items-center justify-center bg-gray-100">
                      <span className="text-2xl">🖼️</span>
                    </div>
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover/image:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <LuEye className="text-white opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 text-lg" />
                    </div>
                  </>
                ) : (
                  /* No URL fallback */
                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                    <span className="text-2xl text-gray-400">🖼️</span>
                  </div>
                )}
              </div>
            </div>

            {/* Image Details */}
            <div className="flex-1 min-w-0">
              <h4 className="text-base font-semibold text-gray-900 truncate mb-2 group-hover:text-gray-800 transition-colors duration-300">
                {getFileName()}
              </h4>

              <div className="space-y-2">
                {/* File metadata */}
                <div className="flex items-center gap-3 text-sm text-gray-600">
                  {getFileSize() && (
                    <span className="font-medium">{getFileSize()}</span>
                  )}
                  {getFileSize() && attachment.type && (
                    <span className="text-gray-400">•</span>
                  )}
                  {attachment.type && (
                    <span className="text-gray-500">{attachment.type}</span>
                  )}
                </div>

                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <LuEye className="w-4 h-4" />
                  <span className="font-medium">Image preview available</span>
                </div>
              </div>
            </div>
          </div>

          {/* Image Action Buttons */}
          <div className="mt-5 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={handleView}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-xl shadow-lg shadow-blue-600/25 hover:shadow-blue-600/40 transition-all duration-300 ease-out hover:scale-105"
              >
                <LuEye className="w-4 h-4" />
                Preview
              </button>

              <button
                onClick={handleDownload}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-300 ease-out hover:scale-105"
              >
                <LuDownload className="w-4 h-4" />
                Download
              </button>
            </div>

            {/* Apple-style Status Indicator */}
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
              <span className="text-sm font-medium text-green-600">Ready</span>
            </div>
          </div>
        </div>
      ) : (
        /* Standard File Content */
        <div className="p-5">
          <div className="flex items-start gap-5">
            {/* Non-Image File Icon */}
            <div className="flex-shrink-0">
              {isPDF ? (
                <div className="relative w-16 h-20 bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-2xl flex flex-col items-center justify-center text-white shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <div className="absolute top-2 right-2 w-2 h-2 bg-white bg-opacity-30 rounded-sm"></div>
                  <div className="text-xs font-bold tracking-wide">PDF</div>
                  <div className="text-lg mt-1">📄</div>
                </div>
              ) : (
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 via-blue-50 to-indigo-100 rounded-2xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300 border border-blue-200/50">
                  <span className="text-2xl">
                    {getFileIcon()}
                  </span>
                </div>
              )}
            </div>

            {/* Apple-style File Details */}
            <div className="flex-1 min-w-0">
              <h4 className="text-base font-semibold text-gray-900 truncate mb-2 group-hover:text-gray-800 transition-colors duration-300">
                {getFileName()}
              </h4>

              <div className="space-y-2">
                {/* File metadata */}
                <div className="flex items-center gap-3 text-sm text-gray-600">
                  {getFileSize() && (
                    <span className="font-medium">{getFileSize()}</span>
                  )}
                  {getFileSize() && isFileObject && attachment.type && (
                    <span className="text-gray-400">•</span>
                  )}
                  {isFileObject && attachment.type && (
                    <span className="text-gray-500">{attachment.type}</span>
                  )}
                </div>

                {canPreview && (
                  <div className="flex items-center gap-2 text-sm text-blue-600">
                    <LuEye className="w-4 h-4" />
                    <span className="font-medium">Preview available</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Apple-style Action Buttons */}
          <div className="mt-5 flex items-center justify-between">
            <div className="flex items-center gap-3">
              {/* Primary Action Button */}
              <button
                onClick={handleView}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-xl shadow-lg shadow-blue-600/25 hover:shadow-blue-600/40 transition-all duration-300 ease-out hover:scale-105"
              >
                {canPreview ? (
                  <>
                    <LuEye className="w-4 h-4" />
                    Preview
                  </>
                ) : (
                  <>
                    <LuSquareArrowOutUpRight className="w-4 h-4" />
                    Open
                  </>
                )}
              </button>

              {/* Secondary Action Button */}
              {isFileObject && (
                <button
                  onClick={handleDownload}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-300 ease-out hover:scale-105"
                >
                  <LuDownload className="w-4 h-4" />
                  Download
                </button>
              )}
            </div>

            {/* Apple-style Status Indicator */}
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
              <span className="text-sm font-medium text-green-600">Ready</span>
            </div>
          </div>
        </div>
      )}

      {/* Apple-style Hover Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/30 opacity-0 group-hover:opacity-100 rounded-2xl transition-all duration-500 pointer-events-none"></div>
    </div>
  );
};

// Comprehensive File Preview Modal Component
const FilePreviewModal = ({ file, onClose }) => {
  const isImage = file.type && file.type.startsWith('image/');
  const isPDF = file.type === 'application/pdf';
  const isVideo = file.type && file.type.startsWith('video/');
  const isAudio = file.type && file.type.startsWith('audio/');
  const isText = file.type && (
    file.type.startsWith('text/') ||
    file.type === 'application/json'
  );

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name || 'download';
    link.click();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="relative bg-white rounded-lg max-w-6xl max-h-[90vh] w-full overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-orange-50">
          <div className="flex items-center gap-3">
            <span className="text-2xl">
              {FileUploadService.getFileIcon(file.type)}
            </span>
            <div>
              <h3 className="text-lg font-medium text-orange-800 truncate">
                {file.name}
              </h3>
              <p className="text-sm text-orange-600">
                {file.size && FileUploadService.formatFileSize(file.size)} • {file.type}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleDownload}
              className="p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-200 rounded-lg transition-colors duration-200"
              title="Download file"
            >
              <LuDownload className="text-lg" />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors duration-200"
              title="Close"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-auto max-h-[calc(90vh-80px)]">
          {isImage && (
            <div className="flex items-center justify-center p-4 bg-gray-50">
              <img
                src={file.url}
                alt={file.name}
                className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                style={{ maxHeight: 'calc(90vh - 160px)' }}
              />
            </div>
          )}

          {isPDF && (
            <div className="h-full">
              <iframe
                src={file.url}
                className="w-full h-full border-0"
                style={{ minHeight: 'calc(90vh - 160px)' }}
                title={file.name}
              />
            </div>
          )}

          {isVideo && (
            <div className="flex items-center justify-center p-4 bg-black">
              <video
                controls
                className="max-w-full max-h-full rounded-lg"
                style={{ maxHeight: 'calc(90vh - 160px)' }}
              >
                <source src={file.url} type={file.type} />
                Your browser does not support the video tag.
              </video>
            </div>
          )}

          {isAudio && (
            <div className="flex flex-col items-center justify-center p-8 bg-gray-50">
              <div className="text-6xl mb-4">🎵</div>
              <audio controls className="w-full max-w-md">
                <source src={file.url} type={file.type} />
                Your browser does not support the audio tag.
              </audio>
              <p className="text-gray-600 mt-4 text-center">{file.name}</p>
            </div>
          )}

          {isText && (
            <div className="p-4">
              <TextFilePreview url={file.url} />
            </div>
          )}

          {!isImage && !isPDF && !isVideo && !isAudio && !isText && (
            <div className="flex flex-col items-center justify-center p-8 text-gray-500">
              <div className="text-6xl mb-4">
                {FileUploadService.getFileIcon(file.type)}
              </div>
              <p className="text-lg font-medium mb-2">Preview not available</p>
              <p className="text-sm text-center mb-4">
                This file type cannot be previewed in the browser.
              </p>
              <button
                onClick={handleDownload}
                className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200"
              >
                <LuDownload className="text-sm" />
                Download File
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
