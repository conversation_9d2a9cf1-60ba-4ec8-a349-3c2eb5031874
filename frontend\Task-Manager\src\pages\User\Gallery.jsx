import React, { useEffect, useState } from "react";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import {
  LuTrophy,
  LuZap,
  LuTarget,
  LuLock,
  LuStar,
  LuCrown,
  LuCheck,
  LuUsers,
  LuTrendingUp,
  LuAward
} from "react-icons/lu";
import moment from "moment";
import toast from "react-hot-toast";
import { useNotifications } from "../../contexts/NotificationContext";

const Gallery = () => {
  // Ensure Gallery function scope is maintained
  const [userInfo, setUserInfo] = useState(null);
  const [allBadges, setAllBadges] = useState([]);
  const [missions, setMissions] = useState([]);
  const [userStats, setUserStats] = useState({});
  const [activeTab, setActiveTab] = useState("badges");
  const [loading, setLoading] = useState(true);
  const { refreshNotifications } = useNotifications();

  // Get appropriate emoji for badge based on name
  const getBadgeEmoji = (badgeName) => {
    const emojiMap = {
      "First Friend": "👥",
      "Profile Pioneer": "👤",
      "Task Initiate": "📝",
      "Swift Finisher": "⚡",
      "Detail Detective": "🔍",
      "Priority Master": "⭐",
      "Connection Creator": "🤝",
      "Team Harmonizer": "🎵",
      "Social Catalyst": "🌟",
      "Daily Dedicated": "📅",
      "Weekly Warrior": "🗓️",
      "Streak Specialist": "🔥",
      "Consistency Champion": "💪",
      "Project Pioneer": "🚀",
      "Milestone Achiever": "🎯",
      "Grand Architect": "🏗️",
      "Feature Explorer": "🧭",
      "Skill Sharpener": "🎓",
      "Level Up": "📈"
    };
    return emojiMap[badgeName] || "🏆";
  };

  // Fix XP synchronization and clean up duplicates (runs automatically in background)
  const fixUserXP = async () => {
    try {
      // First clean up duplicates silently
      const cleanupResponse = await axiosInstance.post("/api/gamification/cleanup-badges");
      if (cleanupResponse.data.success) {
        console.log("Duplicates cleaned automatically:", cleanupResponse.data);
      }

      // Then fix XP synchronization silently
      const response = await axiosInstance.post("/api/gamification/fix-xp");
      if (response.data.success) {
        console.log("XP synchronized automatically:", response.data.data);
        // Refresh user data after fixes
        const userRes = await axiosInstance.get("/api/auth/profile");
        const cleanedUserData = cleanupBadgesAutomatically(userRes.data);
        setUserInfo(cleanedUserData);
      }
    } catch (error) {
      console.error("Error fixing XP automatically:", error);
      // Fail silently - no user notification for automatic background fixes
    }
  };

  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [userRes, badgeRes, missionRes, tasksRes] = await Promise.all([
          axiosInstance.get("/api/auth/profile"),
          axiosInstance.get("/api/gamification/badges"),
          axiosInstance.get("/api/gamification/missions"),
          axiosInstance.get(API_PATHS.TASKS.GET_ALL_TASKS)
        ]);

        // Automatically cleanup duplicate badges
        const cleanedUserData = cleanupBadgesAutomatically(userRes.data);

        setUserInfo(cleanedUserData);
        setAllBadges(badgeRes.data);
        setMissions(missionRes.data);

        // Calculate user statistics for mission progress
        const tasks = tasksRes.data?.tasks || [];
        const stats = calculateUserStats(tasks, cleanedUserData);
        setUserStats(stats);

        // Fix XP synchronization after loading data
        await fixUserXP();
      } catch (err) {
        console.error("Failed to fetch gamification data", err);
        toast.error("Failed to load achievement data");
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  // Calculate user statistics for mission progress tracking
  const calculateUserStats = (tasks, user) => {
    const completedTasks = tasks.filter(task => task.status === 'Completed');
    const totalTasks = tasks.length;

    return {
      totalTasks,
      completedTasks: completedTasks.length,
      tasksWithDetails: tasks.filter(task => task.description && task.description.length > 10).length,
      highPriorityTasks: tasks.filter(task => task.priority === 'High').length,
      tasksCompletedEarly: completedTasks.filter(task =>
        moment(task.updatedAt).isBefore(moment(task.dueDate))
      ).length,
      friendsCount: user?.friends?.length || 0,
      profileComplete: !!(user?.name && user?.email && user?.profileImageUrl),
      currentStreak: calculateStreak(completedTasks),
      level: Math.floor((user?.points || 0) / 100) + 1,
      pointsToNextLevel: 100 - ((user?.points || 0) % 100)
    };
  };

  // Calculate consecutive days streak
  const calculateStreak = (completedTasks) => {
    if (completedTasks.length === 0) return 0;

    const sortedTasks = completedTasks
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    let streak = 0;
    let currentDate = moment().startOf('day');

    for (let task of sortedTasks) {
      const taskDate = moment(task.updatedAt).startOf('day');
      if (taskDate.isSame(currentDate)) {
        streak++;
        currentDate.subtract(1, 'day');
      } else if (taskDate.isBefore(currentDate)) {
        break;
      }
    }

    return streak;
  };

  // Check if mission is actually completed in database
  const isMissionCompleted = (mission) => {
    return userInfo?.completedMissions?.includes(mission.id.toString()) ||
           userInfo?.completedMissions?.includes(mission.id);
  };

  // Check if mission requirements are met (ready to claim)
  const areMissionRequirementsMet = (mission) => {
    switch (mission.id) {
      // Onboarding (Very Easy)
      case 1: return userStats.profileComplete;
      case 2: return userStats.totalTasks > 0;
      case 3: return userStats.friendsCount > 0;

      // Basic Task Management (Easy)
      case 4: return userStats.completedTasks >= 1; // Complete 1 task (was: within 1 hour)
      case 5: return userStats.tasksWithDetails >= 3; // 3 detailed tasks (was: 10)
      case 6: return userStats.highPriorityTasks >= 5; // 5 high priority (was: 20)

      // Social (Easy)
      case 7: return userStats.friendsCount >= 3; // 3 friends (was: 10)
      case 8: return userStats.completedTasks >= 5; // 5 tasks total (was: shared tasks)
      case 9: return userStats.friendsCount >= 10; // 10 friends (was: 50+)

      // Consistency (Moderate)
      case 10: return userStats.currentStreak >= 3; // 3-day streak (was: 7)
      case 11: return userStats.completedTasks >= 10; // 10 tasks total (was: weekly for 4 weeks)
      case 12: return userStats.currentStreak >= 7; // 7-day streak (was: 30)

      // Efficiency (Easy-Moderate)
      case 13: return userStats.completedTasks >= 3; // 3 tasks in total (was: 5 in one sitting)
      case 14: return userStats.tasksCompletedEarly >= 3; // 3 early completions (was: 10)
      case 15: return userStats.completedTasks >= 15; // 15 tasks total (was: 80% rate for month)

      // Projects (Moderate)
      case 16: return userStats.tasksWithDetails >= 5; // 5 detailed tasks (was: multi-task project)
      case 17: return userStats.completedTasks >= 20; // 20 tasks total (was: 5 milestones)
      case 18: return userStats.completedTasks >= 50; // 50 tasks total (was: 3 complex projects)

      // Learning (Easy)
      case 19: return userStats.totalTasks >= 5; // Create 5 tasks (was: try 5 features)
      case 20: return userStats.completedTasks >= 8; // 8 completed tasks (was: 10 learning tasks)
      case 21: return userStats.completedTasks >= 25; // 25 completed tasks (was: 50 educational)

      // Special (Easy)
      case 22: return userStats.completedTasks >= 2; // 2 tasks total (was: after midnight)
      case 23: return userStats.completedTasks >= 2; // 2 tasks total (was: before 6 AM)
      case 24: return userStats.currentStreak >= 5; // 5-day streak (was: work/personal balance)

      // Mastery (Hard but achievable)
      case 25: return userStats.completedTasks >= 100; // 100 tasks (was: 500)
      case 26: return userStats.friendsCount >= 5; // 5 friends (was: assist 5 friends)
      case 27: return userStats.totalTasks >= 30; // 30 tasks created (was: 3 months planning)

      // Customization (Easy)
      case 28: return userStats.tasksWithDetails >= 5; // 5 detailed tasks (was: 10 categories)
      case 29: return userStats.completedTasks >= 12; // 12 completed tasks (was: custom workflows)
      case 30: return userStats.completedTasks >= 30; // 30 completed tasks (was: productivity system)

      default: return false;
    }
  };
  };

  // Get mission progress percentage (EASIER GOALS)
  const getMissionProgress = (mission) => {
    switch (mission.id) {
      // Onboarding
      case 1: return userStats.profileComplete ? 100 : 50;
      case 2: return userStats.totalTasks > 0 ? 100 : 0;
      case 3: return userStats.friendsCount > 0 ? 100 : 0;

      // Basic Task Management
      case 4: return userStats.completedTasks >= 1 ? 100 : (userStats.completedTasks * 100);
      case 5: return Math.min((userStats.tasksWithDetails / 3) * 100, 100);
      case 6: return Math.min((userStats.highPriorityTasks / 5) * 100, 100);

      // Social
      case 7: return Math.min((userStats.friendsCount / 3) * 100, 100);
      case 8: return Math.min((userStats.completedTasks / 5) * 100, 100);
      case 9: return Math.min((userStats.friendsCount / 10) * 100, 100);

      // Consistency
      case 10: return Math.min((userStats.currentStreak / 3) * 100, 100);
      case 11: return Math.min((userStats.completedTasks / 10) * 100, 100);
      case 12: return Math.min((userStats.currentStreak / 7) * 100, 100);

      // Efficiency
      case 13: return Math.min((userStats.completedTasks / 3) * 100, 100);
      case 14: return Math.min((userStats.tasksCompletedEarly / 3) * 100, 100);
      case 15: return Math.min((userStats.completedTasks / 15) * 100, 100);

      // Projects
      case 16: return Math.min((userStats.tasksWithDetails / 5) * 100, 100);
      case 17: return Math.min((userStats.completedTasks / 20) * 100, 100);
      case 18: return Math.min((userStats.completedTasks / 50) * 100, 100);

      // Learning
      case 19: return Math.min((userStats.totalTasks / 5) * 100, 100);
      case 20: return Math.min((userStats.completedTasks / 8) * 100, 100);
      case 21: return Math.min((userStats.completedTasks / 25) * 100, 100);

      // Special
      case 22: return Math.min((userStats.completedTasks / 2) * 100, 100);
      case 23: return Math.min((userStats.completedTasks / 2) * 100, 100);
      case 24: return Math.min((userStats.currentStreak / 5) * 100, 100);

      // Mastery
      case 25: return Math.min((userStats.completedTasks / 100) * 100, 100);
      case 26: return Math.min((userStats.friendsCount / 5) * 100, 100);
      case 27: return Math.min((userStats.totalTasks / 30) * 100, 100);

      // Customization
      case 28: return Math.min((userStats.tasksWithDetails / 5) * 100, 100);
      case 29: return Math.min((userStats.completedTasks / 12) * 100, 100);
      case 30: return Math.min((userStats.completedTasks / 30) * 100, 100);

      default: return isMissionCompleted(mission) ? 100 : 0;
    }
  };

  // Check if badge should be unlocked - UNIFIED APPROACH
  const isBadgeUnlocked = (badge) => {
    // First check if it's already in the user's badges array
    const alreadyOwned = userInfo?.badges?.includes(badge.name);
    if (alreadyOwned) {
      return true;
    }

    // Then check if the related mission is completed
    const relatedMission = missions.find(m => m.badgeName === badge.name);
    if (relatedMission && isMissionCompleted(relatedMission)) {
      return true;
    }

    return false;
  };

  // Get accurate badge count
  const getUnlockedBadgeCount = () => {
    if (!allBadges || !allBadges.length) return 0;
    return allBadges.filter(badge => isBadgeUnlocked(badge)).length;
  };

  // Manual XP sync for testing
  const manualSyncXP = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.post("/api/gamification/fix-xp");
      if (response.data.success) {
        const { totalXP, level } = response.data.data;
        setUserInfo(prev => ({
          ...prev,
          points: totalXP,
          level: level
        }));
        toast.success(`XP synchronized! Total: ${totalXP} XP, Level: ${level}`);
      }
    } catch (error) {
      console.error("Error syncing XP:", error);
      toast.error("Failed to sync XP");
    } finally {
      setLoading(false);
    }
  };

  const completeMission = async (missionId) => {
    try {
      console.log('Attempting to complete mission:', missionId);
      const response = await axiosInstance.post(`/api/gamification/missions/complete/${missionId}`);
      console.log('Mission completion response:', response.data);

      if (response.data.success) {
        const { badgeAwarded, pointsAwarded, newLevel, totalPoints } = response.data;

        // Show success message with details
        let message = `Mission completed! +${pointsAwarded} XP`;
        if (badgeAwarded) {
          message += ` • Badge earned: ${badgeAwarded}`;
        }
        if (newLevel > userInfo.level) {
          message += ` • Level up! Now level ${newLevel}`;
        }

        toast.success(message, { duration: 4000 });

        // Refresh notifications to show mission completion notifications
        setTimeout(() => refreshNotifications(), 1000);

        // Update user info immediately for better UX
        setUserInfo(prev => ({
          ...prev,
          points: totalPoints,
          level: newLevel,
          badges: badgeAwarded && !prev.badges.includes(badgeAwarded)
            ? [...prev.badges, badgeAwarded]
            : prev.badges,
          completedMissions: [...(prev.completedMissions || []), missionId.toString()]
        }));
      }
    } catch (error) {
      console.error("Error completing mission:", error);
      console.error("Error response:", error.response?.data);

      if (error.response?.status === 400) {
        const message = error.response?.data?.message || "Mission already completed";
        console.log(`Mission ${missionId} - ${message}`);
        // Don't show toast for "already completed" to reduce spam
        if (!message.includes("already completed")) {
          toast.error(message);
        }
      } else if (error.response?.status === 401) {
        toast.error("Please log in to complete missions");
      } else if (error.response?.status === 404) {
        console.log(`Mission ${missionId} not found`);
        toast.error("Mission not found");
      } else {
        toast.error(`Failed to complete mission: ${error.response?.data?.message || error.message}`);
      }
    }
  };

  // Automatically cleanup duplicate badges when user data loads
  const cleanupBadgesAutomatically = (userData) => {
    if (!userData.badges) return userData;

    const originalBadgeCount = userData.badges.length;
    const uniqueBadges = [...new Set(userData.badges)];

    if (originalBadgeCount !== uniqueBadges.length) {
      console.log(`Auto-cleaned badges: ${originalBadgeCount} → ${uniqueBadges.length}`);
      return {
        ...userData,
        badges: uniqueBadges
      };
    }

    return userData;
  };



  // Auto-complete missions when requirements are met (CONTROLLED)
  useEffect(() => {
    if (!userInfo || !missions.length || userStats.totalTasks === undefined) return;

    const completableMissions = missions.filter(mission => {
      // Check if requirements are met but not yet marked as completed in database
      const requirementsMet = (() => {
        switch (mission.id) {
          case 1: return userStats.profileComplete;
          case 2: return userStats.totalTasks > 0;
          case 3: return userStats.friendsCount > 0;
          case 4: return userStats.completedTasks >= 1;
          case 5: return userStats.tasksWithDetails >= 3;
          default: return false;
        }
      })();

      // Check both string and number formats to prevent duplicates
      const alreadyCompleted = userInfo?.completedMissions?.includes(mission.id.toString()) ||
                              userInfo?.completedMissions?.includes(mission.id);

      // Additional check: only auto-complete basic missions (1-5) to prevent issues
      const isBasicMission = mission.id <= 5;

      return requirementsMet && !alreadyCompleted && isBasicMission;
    });

    console.log('Completable missions:', completableMissions.map(m => ({ id: m.id, title: m.title })));

    // Only auto-complete if there's exactly 1 mission to prevent spam
    if (completableMissions.length === 1) {
      const mission = completableMissions[0];
      console.log('Auto-completing mission:', mission.id, mission.title);

      // Add delay to prevent rapid-fire requests
      setTimeout(() => {
        completeMission(mission.id);
      }, 1000);
    }
  }, [userStats.completedTasks, userStats.totalTasks, userStats.friendsCount, userInfo?.completedMissions?.length]);

  // Removed duplicate auto-completion function to prevent conflicts

  // Disabled second auto-completion to prevent duplicates
  // useEffect(() => {
  //   if (userInfo && missions.length > 0 && userStats.totalTasks !== undefined) {
  //     checkAndCompleteMissions();
  //   }
  // }, [userStats, userInfo, missions]);

  // Main render logic
  const renderContent = () => {
    if (loading) {
    return (
      <DashboardLayout activeMenu="Gallery">
        <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">Loading achievements...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeMenu="Gallery">
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        {/* Apple-style Header */}
        <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
          <div className="max-w-6xl mx-auto px-6 py-6">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                🏆 Achievement Gallery
              </h1>
              <p className="text-gray-600">
                Track your progress and unlock amazing badges
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-6xl mx-auto px-6 py-8">
          {/* User Level Card */}
          <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-purple-200/50 border border-purple-200/30 p-8 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl flex items-center justify-center">
                    <LuCrown className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                    {userInfo?.level || 1}
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Level {userInfo?.level || 1}</h3>
                  <p className="text-gray-600 flex items-center gap-2">
                    <LuZap className="w-4 h-4 text-yellow-500" />
                    {userInfo?.points || 0} XP
                  </p>
                </div>
              </div>

              <div className="text-right">
                <p className="text-sm text-gray-600 mb-2">Next Level</p>
                <div className="w-48 bg-gray-200 rounded-full h-3 mb-2">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-3 rounded-full transition-all duration-500"
                    style={{
                      width: `${((userInfo?.points || 0) % 100)}%`
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">{100 - ((userInfo?.points || 0) % 100)} XP to go</p>
              </div>

              {/* Debug button - remove in production */}
              <button
                onClick={manualSyncXP}
                className="ml-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                disabled={loading}
              >
                {loading ? "Syncing..." : "Sync XP"}
              </button>
            </div>
          </div>

          {/* Apple-style Tabs */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100/80 backdrop-blur-sm p-2 rounded-2xl shadow-sm">
              <button
                onClick={() => setActiveTab("badges")}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ease-out ${
                  activeTab === "badges"
                    ? "bg-white text-purple-600 shadow-lg shadow-purple-600/20 scale-105"
                    : "text-gray-600 hover:bg-white/50"
                }`}
              >
                <LuTrophy className="w-5 h-5" />
                <span className="font-medium">Badges</span>
              </button>
              <button
                onClick={() => setActiveTab("missions")}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ease-out ${
                  activeTab === "missions"
                    ? "bg-white text-purple-600 shadow-lg shadow-purple-600/20 scale-105"
                    : "text-gray-600 hover:bg-white/50"
                }`}
              >
                <LuTarget className="w-5 h-5" />
                <span className="font-medium">Missions</span>
              </button>
            </div>
          </div>

          {/* Apple-style Badges Section */}
          {activeTab === "badges" && (
            <div className="space-y-6">
              {/* Stats Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/30">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <LuCheck className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{getUnlockedBadgeCount()}</p>
                      <p className="text-sm text-gray-600">Badges Earned</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/30">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                      <LuTarget className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{userStats.completedTasks}</p>
                      <p className="text-sm text-gray-600">Tasks Completed</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/30">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                      <LuTrendingUp className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{userStats.currentStreak}</p>
                      <p className="text-sm text-gray-600">Day Streak</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/30">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                      <LuUsers className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{userStats.friendsCount}</p>
                      <p className="text-sm text-gray-600">Friends</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Badges Grid */}
              <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Badge Collection</h3>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
                  {allBadges.map((badge, index) => {
                    const unlocked = isBadgeUnlocked(badge);
                    return (
                      <div
                        key={index}
                        className={`relative group rounded-2xl p-4 text-center border transition-all duration-300 hover:scale-105 ${
                          unlocked
                            ? "bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 shadow-lg shadow-blue-200/50"
                            : "bg-gray-100 border-gray-300 opacity-60"
                        }`}
                        title={unlocked ? badge.description : "🔒 Locked - Complete missions to unlock"}
                      >
                        {!unlocked && (
                          <div className="absolute top-2 right-2 w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                            <LuLock className="w-3 h-3 text-white" />
                          </div>
                        )}

                        {unlocked && (
                          <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <LuCheck className="w-3 h-3 text-white" />
                          </div>
                        )}

                        <div className="relative">
                          {/* Use emoji icons directly for better reliability */}
                          <div
                            className={`w-16 h-16 mx-auto mb-3 rounded-xl bg-gradient-to-br from-blue-100 to-purple-100 border-2 border-blue-200 flex items-center justify-center text-3xl transition-all duration-300 ${!unlocked ? "grayscale opacity-50" : "hover:scale-110"}`}
                          >
                            {getBadgeEmoji(badge.name)}
                          </div>
                          {unlocked && (
                            <div className="absolute inset-0 bg-gradient-to-t from-yellow-400/20 to-transparent rounded-xl"></div>
                          )}
                        </div>

                        <h4 className="font-semibold text-sm text-gray-900 mb-1">{badge.name}</h4>
                        <p className="text-xs text-gray-600 leading-relaxed">{badge.description}</p>

                        {badge.rarity && (
                          <div className={`mt-2 inline-block px-2 py-1 rounded-full text-xs font-medium ${
                            badge.rarity === 'legendary' ? 'bg-yellow-100 text-yellow-800' :
                            badge.rarity === 'epic' ? 'bg-purple-100 text-purple-800' :
                            badge.rarity === 'rare' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {badge.rarity}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Apple-style Missions Section */}
          {activeTab === "missions" && (
            <div className="space-y-6">
              {/* Mission Categories */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Active Missions */}
                <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                      <LuTarget className="w-5 h-5 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Active Missions</h3>
                  </div>

                  <div className="space-y-4">
                    {missions.filter(mission => !isMissionCompleted(mission)).slice(0, 5).map((mission, index) => {
                      const progress = getMissionProgress(mission);
                      const canClaim = areMissionRequirementsMet(mission);
                      return (
                        <div
                          key={index}
                          className="group p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-200/50 hover:shadow-lg transition-all duration-300"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h4 className="font-semibold text-gray-900 mb-1">{mission.title}</h4>
                              <p className="text-sm text-gray-600 leading-relaxed">{mission.description}</p>
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                              <div className="flex items-center gap-1 text-blue-600 text-sm font-semibold bg-blue-100 px-3 py-1 rounded-xl">
                                <LuZap className="w-4 h-4" />
                                +{mission.points} XP
                              </div>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="mb-3">
                            <div className="flex justify-between text-xs text-gray-600 mb-1">
                              <span>Progress</span>
                              <span>{Math.round(progress)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${progress}%` }}
                              ></div>
                            </div>
                          </div>

                          {canClaim && (
                            <button
                              onClick={() => completeMission(mission.id)}
                              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold py-2 px-4 rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-105"
                            >
                              <LuCheck className="w-4 h-4 inline mr-2" />
                              Claim Reward
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Completed Missions */}
                <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <LuAward className="w-5 h-5 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Completed Missions</h3>
                  </div>

                  <div className="space-y-4">
                    {missions.filter(mission => isMissionCompleted(mission)).map((mission, index) => (
                      <div
                        key={index}
                        className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl border border-green-200/50"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <LuCheck className="w-4 h-4 text-green-600" />
                              <h4 className="font-semibold text-gray-900">{mission.title}</h4>
                            </div>
                            <p className="text-sm text-gray-600">{mission.description}</p>
                          </div>
                          <div className="flex items-center gap-1 text-green-600 text-sm font-semibold bg-green-100 px-3 py-1 rounded-xl ml-4">
                            <LuZap className="w-4 h-4" />
                            +{mission.points} XP
                          </div>
                        </div>
                      </div>
                    ))}

                    {missions.filter(mission => isMissionCompleted(mission)).length === 0 && (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                          <LuTarget className="w-8 h-8 text-gray-400" />
                        </div>
                        <p className="text-gray-600">No completed missions yet</p>
                        <p className="text-sm text-gray-500">Complete your first mission to see it here!</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Gallery;
